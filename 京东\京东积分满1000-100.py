import requests

url = "https://api.m.jd.com/api?functionId=bff_rights_points&scene=serviceDetail"

payload = {
  'functionId': "bff_rights_points",
  'body': "{\"categoryId\":\"409902\",\"serviceType\":\"cjbtq2\",\"status\":5,\"anchoringSkuId\":\"957045684\",\"scene\":\"serviceDetail\",\"sourceCode\":\"chaojibutie\"}",
  'appid': "plus_business",
  'loginType': "1",
  'loginWQBiz': "plus",
  'scval': "",
  'x-api-eid-token': "jdd03ZHE5TCX7LWH7HH3S3Q5D2IIUOYKX6B44BFHKG5M4RBC5457KHM7PMHUOUD6H6E4PEMW74P3VK5NLY2ZOODYZCDI4IYAAAAMXJQ5KO3AAAAAACLNIKM4EUSFLVQX",
  'h5st': "20250608100651998;pi9agd3dpwj3maj5;b63ff;tk03wa4e41c0918nBrXstZ112WlPiEAWEW2uyBHzjxDXbhCKTVV9bm4a4IUuhYkhDN9oAogqFpBXPGB5OKViF7hIuz2h;862384ec225116f7eb21319b62f025defa11da5618c304af338c545bfc1a9242;5.1;1749348406998;ri_uxFOm5GYWBhLU3tnV7e4VNJbU_lsm0msSIlsmOGuj4qrm0mMTLhImOuMsCmsi1ubiIhoh_Krg7eYWLloh_qriKlohNNbiMpbW9KLh2msm0msSo94VMZ4RMusmk_Mm_aog1eLiJJ7h7m7i2irh7urVLp4iLpbh_eohLtbW8mLmOGLm7pIRAp4WMusmk_siOGLm6aHWMusmk_Mm52ciAaLRPZoTFV4X5OImOGLm4lsmOGujMKYZARJT8eLVPlsm0mcT-dITNlHmOuMsCmMi72YUXlsm0mMV_lsmOGujxtsmkmrm0mci9aHWMusmOuMsCmbiOGLm_qbRMlsmOusmk_sgBuMgMmbi5lImOusmOGujMapY9OrTaB4X8qZTWlsm0mcT-dITNlHmOusmOGuj_uMgMObRMlsmOusmk_siOGLm3aHWMusmOuMsCOLiOGLm4aHWMusmOuMsCurm0mch5lImOusmOGuj_uMgMebRMlsmOusmk_8i8uMgMibRMlsmOusmk_Mm52ciAuLmOGLm9aHWMusmOuMsCurm0m8U3lsmOusmk_ciBuMgMinTMusmOuMsCurm0msTMusmOuMsCurm0msV3lsmOusmkCnm0msVAZoR2ZImOuMsC6nmOGOmCdIb95aRNZ5S1FaUPdIUMuMgMmrSMusmOuMsztMgMunSMusmk_Mm6WrQOCrh42YUXt8g_2si9usZgt8S3xoVAJ4ZMuMgMqYR7lsmOG_Q;6bf0b8d540686e7b94e7ac18dbf4b8d0fa3ba335d609dd90aa497b399a571687;ri_uKJKT-JoRL1YRI9cQKxIWCeYU_tXW"
}

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c33) XWEB/13639 Flue",
  'Accept': "application/json, text/plain, */*",
  'x-rp-client': "h5_1.0.0",
  'x-referer-page': "https://plus.m.jd.com/page/oss/rights/points/index",
  'origin': "https://plus.m.jd.com",
  'sec-fetch-site': "same-site",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://plus.m.jd.com/page/oss/rights/points/index?tab1=dacu1&sourceCode=chaojibutie&utm_user=plusmember&_ts=1749312074177&ad_od=share&gxd=RnAokW4LbGffzskdr9V0W9nPbsQ0ZXNL2JxNmpx3V6TpNqG9bRs4fVmTzukkXEk&gx=RnAomTM2bz2Nyp4cptV_CY36BGN7Ivo&fom=jd.com&rid=18133&cu=true&hideyl=1&utm_source=lianmeng__2__kong&utm_medium=jingfen&utm_campaign=t_2020796581_&utm_term=b0ba924fbba6462692dc9a293c263941",
  'accept-language': "zh-CN,zh;q=0.9",
  'priority': "u=1, i",
  'Cookie': "__jdc=122270672; mba_muid=1749330351793132788106; b_dw=905; b_dh=801; b_dpr=1; b_webp=1; b_avif=1; 3AB9D23F7A4B3C9B=ZHE5TCX7LWH7HH3S3Q5D2IIUOYKX6B44BFHKG5M4RBC5457KHM7PMHUOUD6H6E4PEMW74P3VK5NLY2ZOODYZCDI4IY; shshshfpa=47bceaf8-743f-ef19-3148-5deb937ff57c-1738669845; shshshfpx=47bceaf8-743f-ef19-3148-5deb937ff57c-1738669845; unpl=JF8EAKFnNSttC0oHVRMBHkVDGA1TW1oLTB8AOjUNBF5QSQYGHAATFxB7XlVdWRRKEB9ubhRVWVNKXA4eAysSEHteVV1cDkMQB2ZnNWRVUCVUSBtsGHwQBhAZbl4IexYzb2EAXF9aSlYBGwQTEBBJXlJXVAhKEDNuVwVSbWh7VTUbCxsQEntcZBUzCQYXBWpvB1ZcWk9UAxMAGxATTVRdXlwPexYzbA; __jda=122270672.1749330351793132788106.1749330351.1749348101.1749348214.3; wxa_level=1; qid_seq=1; qid_uid=ceaa2515-5a30-48f8-b1ad-a2daf7f57c29; qid_fs=1749348214632; qid_ls=1749348214632; qid_ts=1749348214650; qid_vis=1; qid_sid=ceaa2515-5a30-48f8-b1ad-a2daf7f57c29-1; shshshfpb=BApXSRs4yT_JASkTohngBrYOBNJwouUSmBnonn2oU9xJ1MgySWY62; bf_hybrid=d_-; refer_v=real-url; rurl=%2F%2Fwq.jd.com%2Fpinbind%2Fpintokenredirect%3Ftype%3D1%26biz%3Dplus%26rurl%3Dhttps%253A%252F%252Fplus.m.jd.com%252Fpage%252Foss%252Frights%252Fpoints%252Findex%253Ftab1%253Ddacu1%2526sourceCode%253Dchaojibutie%2526utm_user%253Dplusmember%2526_ts%253D1749312074177%2526ad_od%253Dshare%2526gxd%253DRnAokW4LbGffzskdr9V0W9nPbsQ0ZXNL2JxNmpx3V6TpNqG9bRs4fVmTzukkXEk%2526gx%253DRnAomTM2bz2Nyp4cptV_CY36BGN7Ivo%2526fom%253Djd.com%2526rid%253D18133%2526cu%253Dtrue%2526hideyl%253D1%2526utm_source%253Dlianmeng__2__kong%2526utm_medium%253Djingfen%2526utm_campaign%253Dt_2020796581_%2526utm_term%253Db0ba924fbba6462692dc9a293c263941%26scope%3D0%26sceneid%3D9001%26btnTips%3D%26hideApp%3D0; TrackID=O5FGPDtOi1pqODF97LTOP4omvM6xFgShRu9aSyGfTJABw8_ynNZrj3bBK2ujDiNFM-P1M1wQhqCrD-qNvMguWAoEFQRaQKJEEQKyisYr114-oEkf02CuLJxFM3bf92vE-nxwWkNcy_1l2io91w9EYRz8geQ-qrzZ0BjUi0i_w4M; buy_uin=5444291456; jdpin=jd_iJysUauMqNdl; mcossmd=dad13a6f17381ee5a115e930218390ad; nickname_base64=; open_id=oTGnpnJNG-2gRGrwqIpJ8FtBmS9c; openid2=DB2CC6E6B87893314B53A213F453655FD6F604C618AAE484CD1216D1140CE6727A3DC3E703BC8847A5E93C9CF3E00F9C; picture_url=; pin=jd_iJysUauMqNdl; pinId=G7rGJpEp-zRILr7QqU2gjQ; pinStatus=0; pinsign=f62a6c31147247541ee45ecf1ba20ad9; sex=0; wq_skey=za4FC0C25923775B9EADA308989C0107F11989602CB538C33F29618B13D85DD0C85A05DFFD9981C402EFF37A0F30D31F6232EBFA7200C969132E6A0DD23F2E2A89F61ABAC89C93C012BCBC5E6A6BF17161; wq_uin=5444291456; wq_unionid=oCwKwuK32ubDlXibnJBc0LmIkUxg; wx_nickname=jd_user; country=; sfstoken=tk01waee81c38a8sMXgzRGRGeVhEOC14j%2BZOZjoPXJeuBDVPuWz3QNbRZa6YDleCBuznrRH7EVHof3PMSz5XdhNmOX2b; wq_auth_token=06AB6513A1458799C58385255FB0776F2B2BDECD6CE7C167EA968C9DC1612E65C5EE73963AB1CEDABA6C444F022CB6C9; qid_evord=2; p-request-id=jd_iJysUauMqNdl2025060805fBIWuBGnf5; __jdb=122270672.3.1749330351793132788106|3.1749348214; __jdv=122270672%7Clianmeng__2__kong%7Ct_2020796581_%7Cjingfen%7Cb0ba924fbba6462692dc9a293c263941%7C1749348235103; mba_sid=17493481014381406691129.4; 3AB9D23F7A4B3CSS=jdd03ZHE5TCX7LWH7HH3S3Q5D2IIUOYKX6B44BFHKG5M4RBC5457KHM7PMHUOUD6H6E4PEMW74P3VK5NLY2ZOODYZCDI4IYAAAAMXJQ5KO3AAAAAACLNIKM4EUSFLVQX; joyya=1749348214.1749348342.58.1phfu11; __jd_ref_cls=Mmyplus_lifeservice_redebtn; sdtoken=AAbEsBpEIOVjqTAKCQtvQu17Kf4Oa8pjwm1tpdyOrAfELnkgIvk9xTNTQxMohIj67UDwrNMxWIogO1gGN_rAYfUfdqapdw6etIAqu5B0PFvpAund_-Nah1M_3-WfAJobeBqp"
}

response = requests.post(url, data=payload, headers=headers)

print(response.text)

# 兑换功能实现
exchange_url = "https://api.m.jd.com/api?functionId=bff_rights_points_exchange&scene=serviceDetail"
exchange_payload = {
    'functionId': "bff_rights_points_exchange",
    'body': '{"categoryId":"409902","serviceType":"cjbtq2","anchoringSkuId":"957045684","scene":"serviceDetail","sourceCode":"chaojibutie"}',
    'appid': "plus_business",
    'loginType': "1",
    'loginWQBiz': "plus",
    'scval': "",
    'x-api-eid-token': payload['x-api-eid-token'],
    'h5st': payload['h5st']
}

exchange_headers = headers.copy()

# 发送兑换请求
exchange_response = requests.post(exchange_url, data=exchange_payload, headers=exchange_headers)

print("兑换响应:", exchange_response.text)

# 简单分析响应内容
try:
    result = exchange_response.json()
    if 'success' in result and result['success']:
        print("兑换成功！")
    elif 'msg' in result:
        print("兑换失败：", result['msg'])
    else:
        print("兑换结果未知，返回：", result)
except Exception as e:
    print("无法解析兑换响应：", e)