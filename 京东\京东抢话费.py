import aiohttp
import asyncio
import datetime
import time
import ssl
import requests


def send(message, to_user=""):
    # 强制转换 message 为字符串
    message = str(message)
    
    # 参数校验
    if not message.strip():
        raise ValueError("消息内容不能为空")
    if not to_user.startswith("wxid_"):
        print("警告:接收用户ID格式可能无效")

    url = ""
    #params = {"key": ""}
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json",
    }
    data = {
        "MsgItem": [
            {
            "AtWxIDList": [
                "string"
            ],
            "ImageContent": "",
            "MsgType": 0,
            "TextContent": message,
            "ToUserName": to_user
            }
        ]
    }
    #print(data)
    try:
        response = requests.post(url,headers=headers, json=data, timeout=5)
        print("状态码:", response.status_code)
        print("响应内容:", response.json())
        return response
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None








# 创建不验证证书的SSL上下文
ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE

#url = "https://api.m.jd.com/client.action?functionId=newBabelAwardCollection"

#url = "https://api.m.jd.com/client.action?functionId=newBabelAwardCollection"

url = "https://api.m.jd.com/api?functionId=SEP_EXCHANGE_SUBMIT"

payload = {
  'appid': "h5-sep",
  'functionId': "SEP_EXCHANGE_SUBMIT",
  'body': "{\"businessType\":118502,\"activityId\":810602,\"quantity\":1,\"score\":2,\"remark\":\"\",\"uuid\":\"75c27dd0-f415-cd5c-1566-13e5f8cfa073\",\"addrCode\":5404598669,\"exchangeWareType\":2,\"wareBusinessId\":\"1192685891\",\"deliveryMode\":3,\"activityWareId\":1180704,\"parentActivityWareId\":null,\"parentActivityId\":null,\"stockType\":1,\"upperBusinessType\":null,\"orderType\":2,\"userName\":\"薛薛薛\",\"phoneNum\":\"***********\",\"userAddress\":\"广东清远市清城区东城街道象牙岭家和微超\",\"provinceId\":19,\"cityId\":1704,\"countyId\":37734,\"townId\":56600,\"addressDetail\":\"象牙岭家和微超\"}",
  'client': "m",
  'clientVersion': "6.0.0"
}

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c33) XWEB/13639 Flue",
  'x-referer-page': "https://m-sep.jd.com/Settlement",
  'x-api-eid-token': "jdd03UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DMAAAAMXPUP64AYAAAAADU2RA2G6EEYP34X",
  'x-rp-client': "h5_1.0.0",
  'origin': "https://m-sep.jd.com",
  'sec-fetch-site': "same-site",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://m-sep.jd.com/",
  'accept-language': "zh-CN,zh;q=0.9",
  'priority': "u=1, i",
  'Cookie': "__jda=122270672.1750150929483324107970.1750150929.1750150929.1750150929.1; __jdv=122270672%7Cdirect%7C-%7Cnone%7C-%7C1750150929484; __jdc=122270672; mba_muid=1750150929483324107970; 3AB9D23F7A4B3CSS=jdd03UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DMAAAAMXPUP64AYAAAAADU2RA2G6EEYP34X; 3AB9D23F7A4B3C9B=UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DM; _gia_d=1; sepCJSToken=jdd03UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DMAAAAMXPUP64AYAAAAADU2RA2G6EEYP34X; TrackerID=ALWJ94h4GXYOmMLyPJbNwQ-sWKOSECHJer1KXme55arH0lAMhDWPv77_Z9pwIo2IirZ9SS9StdAJgYmKBZQEnB__9r6W81AhLX2Cr8C8FdsMDoh3tU9vjFGMjvushxoC6XsUQEBYyJjm5GMfx736dw; pt_key=AAJoUS8fADAPTsz35xG0cXOXDl2K8hDdk_EimM1m_m9IFcA-rDrfkmoC_2U52nlgNCsSP5g5EYs; pt_pin=jd_IOPnkWamWXQT; pt_token=my73pjp0; pwdt_id=jd_IOPnkWamWXQT; sfstoken=tk01med791d27a8sMXgyKzN4Mjdyl7UpeBfwdzJvUhVEfuyVg/HET+mZFd/uSjpob7TvY+eOo5mh9fQ0WZW4KdQHhcBi; __jdb=122270672.55.1750150929483324107970|1.1750150929; mba_sid=17501509294851566716052.15; sdtoken=AAbEsBpEIOVjqTAKCQtvQu17MWPnNLpwWakl4iISNFIJMemkfm0yg-roSKGiPpuO8R1X6ujpiY50ZJCDCKRWIRiKjtGc8s5MMC0Y8bQjyk1W9P-zkarQ5G8cee4; __jd_ref_cls=JDSEP_SubmitExchange"
}

async def send_request(session: aiohttp.ClientSession):
    """异步发送请求（禁用证书验证）"""
    try:
        async with session.post(url, data=payload, headers=headers) as resp:
            return await resp.text()
    except aiohttp.ClientError as e:
        print(f"请求错误: {e}")
        return None


async def get_jd_time() -> tuple[float, float]:
    """获取京东服务器时间和时间差（异步请求）"""
    jd_time_url = "https://api.m.jd.com/client.action?functionId=queryMaterialProducts&client=wh5"
    try:
        async with aiohttp.ClientSession() as session:
            request_start = time.time()
            async with session.head(jd_time_url, timeout=3) as response:
                request_end = time.time()
                # 计算网络延迟
                network_delay = (request_end - request_start) / 2

                # 从响应头中提取服务器时间（UTC+8）
                server_time_str = response.headers.get('Date', '')
                if server_time_str:
                    server_time = datetime.datetime.strptime(
                        server_time_str, '%a, %d %b %Y %H:%M:%S GMT'
                    ).timestamp() + 28800 + network_delay  # GMT+8时区校准 + 网络延迟补偿

                    local_time = time.time()
                    time_diff = server_time - local_time  # 服务器时间 - 本地时间

                    print(f"京东服务器时间: {datetime.datetime.fromtimestamp(server_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
                    print(f"本地系统时间: {datetime.datetime.fromtimestamp(local_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
                    print(f"网络延迟: {network_delay:.3f}秒")
                    print(f"时间差异: {time_diff:.3f}秒 (正数表示服务器时间快)")

                    return server_time, time_diff
                else:
                    raise Exception("无法获取服务器时间头")

    except Exception as e:
        print(f"获取京东时间失败: {e}")
        local_time = time.time()
        print(f"使用本地时间: {datetime.datetime.fromtimestamp(local_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        return local_time, 0.0  # 降级为本地时间，时间差为0


async def wait_until(target_jd_time: float, advance_ms: int = 100):
    """等待至京东服务器时间到达目标时刻（支持提前抢购）

    Args:
        target_jd_time: 目标时间戳
        advance_ms: 提前毫秒数，默认100毫秒
    """
    # 计算提前时间
    advance_seconds = advance_ms / 1000.0
    target_time_advanced = target_jd_time - advance_seconds

    print(f"提前抢购设置: {advance_ms}毫秒")

    while True:
        current_jd_time, time_diff = await get_jd_time()  # 获取京东时间和时间差
        remaining = target_time_advanced - current_jd_time

        if remaining <= 0:
            print(f"[时间同步] 提前 {advance_ms}ms 开始抢购！")
            break

        # 显示倒计时
        if remaining > 5:
            print(f"[倒计时] 距离抢购还有 {remaining:.1f} 秒")
        elif remaining > 1:
            print(f"[倒计时] 距离抢购还有 {remaining:.2f} 秒")
        else:
            print(f"[倒计时] 距离抢购还有 {remaining:.3f} 秒")

        # 动态调整等待间隔（平衡请求频率和精度）
        if remaining > 5.0:
            await asyncio.sleep(1.0)
        elif remaining > 1.0:
            await asyncio.sleep(min(remaining / 2, 0.5))
        elif remaining > 0.1:
            await asyncio.sleep(0.02)
        else:
            await asyncio.sleep(0.001)





async def main():
    # 定义多个抢购目标时间（京东时区 GMT+8）
    time_points = [
        (17, 18, 59, 899000),
        (9, 59, 59, 899000),
    ]
    now = await get_jd_time()
    today = datetime.datetime.fromtimestamp(now)
    for hour, minute, second, microsecond in time_points:
        # 生成今天的目标时间戳
        target_time = today.replace(hour=hour, minute=minute, second=second, microsecond=microsecond).timestamp()
        # 若已过该时间点则跳过
        if now >= target_time:
            continue
        human_time = datetime.datetime.fromtimestamp(target_time).strftime(
            "%Y年%m月%d日 %H时%M分%S.%f"
        )[:-3] + "秒"
        print(f"[京东同步] 等待至抢购开始时间: {human_time}")
        await wait_until(target_time)
        print(f"[系统] {hour:02d}:{minute:02d} 时间到达，执行抢购！")
        # 创建会话，禁用证书验证
        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context)) as session:
            tasks = [send_request(session) for _ in range(50)]
            responses = await asyncio.gather(*tasks)
            for i, res in enumerate(responses):
                print(f"Request {i+1}: {res}")
                if '领取成功' in res:
                    send('京东100-50领取成功')
        # 抢完后更新时间，防止后续时间点被跳过
        now = await get_jd_time()
    print("[系统] 今日所有抢购时间段已结束")
    


if __name__ == "__main__":
    
    asyncio.run(main())