import requests
import base64
#import ddddocr
import time
import uuid
import os

url_ocr = "http://172.17.0.1:8000//ocr"
url_captcha = "https://xbk.189.cn/xbkapi/api/auth/captcha"

#ocr = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False, import_onnx_path=r"E:\work\vscode1\python\dddd_trainer-main\startDDDD\all_models\电信\星播客抽奖\dxcj_1.0_72_6000_2025-01-02-22-24-56.onnx", charsets_path=r"E:\work\vscode1\python\dddd_trainer-main\projects\dxcj\models\charsets.json")
uuidVluae=uuid.uuid4()

params = {
    'guid': uuidVluae  
}

headers = {
    'User-Agent': "CtClient;11.8.0;Android;12;Redmi K30 Pro;",
    'Accept': "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
    'Accept-Encoding': "gzip, deflate, br, zstd",
    'sec-ch-ua-platform': "\"Android\"",
    'sec-ch-ua': "\"Android WebView\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
    'sec-ch-ua-mobile': "?1",
    'x-requested-with': "com.ct.client",
    'sec-fetch-site': "same-origin",
    'sec-fetch-mode': "no-cors",
    'sec-fetch-dest': "image",
    'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
}

start_time_get_image = time.time()
image_response = requests.get(url_captcha, params=params, headers=headers)
end_time_get_image = time.time()
print(f"获取验证码图片耗时: {(end_time_get_image - start_time_get_image):.3f} 秒")

 #保存图片到本地
#image_dir = "captcha_images"
#if not os.path.exists(image_dir):
#    os.makedirs(image_dir)
#image_path = os.path.join(image_dir, "latest_captcha.png")
#with open(image_path, "wb") as f:
#    f.write(image_response.content)
#print(f"验证码本地图片: {image_path}")

encoded_string = base64.b64encode(image_response.content).decode('utf-8')

data = {
    "image": encoded_string,
    "probability": False,
    "png_fix": False
}

# 本地识别看看
# start_time_local = time.time()
# local_result = ocr.classification(encoded_string)
# end_time_local = time.time()
# print(f"本地识别结果: {local_result}")
# print(f"本地识别耗时: {(end_time_local - start_time_local):.3f} 秒")

# 服务器识别
start_time_server = time.time()
response = requests.post(url_ocr, data=data)
server_result = response.json().get("data")
end_time_server = time.time()
print(f"服务器识别结果: {server_result}")
print(f"服务器请求耗时: {(end_time_server - start_time_server):.3f} 秒")



'''
ls ddddocr-api_latest.tar

如果上传了你会看到这个

导入过来
docker load -i ddddocr-api_latest.tar
可能要几分钟
Loaded image: ddddocr-api:latest


最后看到这个就行了  docker images  查看镜像
ddddocr-api



docker run -d -p 8000:8000 --name ddddocr_api ddddocr-api


看到有容器就行了，我测下。上面改了我服务器ip
跑下这个本，

能看到这个就代表可以了


获取验证码图片耗时: 0.243 秒
服务器识别结果: 38+1=
服务器请求耗时: 0.133 秒

'''