package main

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/google/uuid"
)

// 配置常量
const (
	JD_TIME_URL     = "https://api.m.jd.com/client.action?functionId=queryMaterialProducts&client=wh5"
	EXCHANGE_URL    = "https://api.m.jd.com/api?functionId=SEP_EXCHANGE_SUBMIT"
	CONCURRENT_NUM  = 50           // 并发请求数
	ADVANCE_MS      = 200          // 提前毫秒数
	TARGET_HOUR     = 10           // 目标小时
	TARGET_MINUTE   = 0            // 目标分钟
)

// 兑换请求体结构
type ExchangeBody struct {
	BusinessType         int    `json:"businessType"`
	ActivityID           int    `json:"activityId"`
	Quantity             int    `json:"quantity"`
	Score                int    `json:"score"`
	Remark               string `json:"remark"`
	UUID                 string `json:"uuid"`
	AddrCode             int64  `json:"addrCode"`
	ExchangeWareType     int    `json:"exchangeWareType"`
	WareBusinessID       string `json:"wareBusinessId"`
	DeliveryMode         int    `json:"deliveryMode"`
	ActivityWareID       int    `json:"activityWareId"`
	ParentActivityWareID *int   `json:"parentActivityWareId"`
	ParentActivityID     *int   `json:"parentActivityId"`
	StockType            int    `json:"stockType"`
	UpperBusinessType    *int   `json:"upperBusinessType"`
	OrderType            int    `json:"orderType"`
	UserName             string `json:"userName"`
	PhoneNum             string `json:"phoneNum"`
	UserAddress          string `json:"userAddress"`
	ProvinceID           int    `json:"provinceId"`
	CityID               int    `json:"cityId"`
	CountyID             int    `json:"countyId"`
	TownID               int    `json:"townId"`
	AddressDetail        string `json:"addressDetail"`
	Timestamp            int64  `json:"_t"`
}

// 响应结构
type ExchangeResponse struct {
	Success      bool   `json:"success"`
	ErrorCode    int    `json:"errorCode"`
	ErrorMessage string `json:"errorMessage"`
}

// 京东时间信息
type JDTimeInfo struct {
	ServerTime time.Time
	LocalTime  time.Time
	TimeDiff   time.Duration
	Delay      time.Duration
}

// HTTP客户端配置
var httpClient = &http.Client{
	Timeout: 10 * time.Second,
	Transport: &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		MaxIdleConns:    100,
		IdleConnTimeout: 30 * time.Second,
	},
}

// 获取京东服务器时间
func getJDTime() (*JDTimeInfo, error) {
	start := time.Now()
	
	resp, err := httpClient.Head(JD_TIME_URL)
	if err != nil {
		return nil, fmt.Errorf("请求京东时间失败: %v", err)
	}
	defer resp.Body.Close()
	
	end := time.Now()
	delay := end.Sub(start) / 2 // 网络延迟补偿
	
	// 从响应头获取服务器时间
	dateStr := resp.Header.Get("Date")
	if dateStr == "" {
		return nil, fmt.Errorf("无法获取服务器时间头")
	}
	
	serverTime, err := time.Parse(time.RFC1123, dateStr)
	if err != nil {
		return nil, fmt.Errorf("解析服务器时间失败: %v", err)
	}
	
	// 转换为北京时间并补偿延迟
	beijingTime := serverTime.In(time.FixedZone("CST", 8*3600)).Add(delay)
	localTime := time.Now()
	timeDiff := beijingTime.Sub(localTime)
	
	fmt.Printf("京东服务器时间: %s\n", beijingTime.Format("2006-01-02 15:04:05.000"))
	fmt.Printf("本地系统时间: %s\n", localTime.Format("2006-01-02 15:04:05.000"))
	fmt.Printf("网络延迟: %.3f秒\n", delay.Seconds())
	fmt.Printf("时间差异: %.3f秒 (正数表示服务器时间快)\n", timeDiff.Seconds())
	
	return &JDTimeInfo{
		ServerTime: beijingTime,
		LocalTime:  localTime,
		TimeDiff:   timeDiff,
		Delay:      delay,
	}, nil
}

// 等待到京东目标时间
func waitUntilJDTime(targetTime time.Time, advanceMs int) error {
	advanceDuration := time.Duration(advanceMs) * time.Millisecond
	targetTimeAdvanced := targetTime.Add(-advanceDuration)
	
	fmt.Printf("提前抢购设置: %d毫秒\n", advanceMs)
	
	for {
		jdTimeInfo, err := getJDTime()
		if err != nil {
			fmt.Printf("获取京东时间失败: %v\n", err)
			time.Sleep(1 * time.Second)
			continue
		}
		
		remaining := targetTimeAdvanced.Sub(jdTimeInfo.ServerTime)
		
		if remaining <= 0 {
			fmt.Printf("[时间同步] 提前 %dms 开始抢购！\n", advanceMs)
			break
		}
		
		// 显示倒计时
		if remaining > 5*time.Second {
			fmt.Printf("[倒计时] 距离抢购还有 %.1f 秒\n", remaining.Seconds())
			time.Sleep(1 * time.Second)
		} else if remaining > 1*time.Second {
			fmt.Printf("[倒计时] 距离抢购还有 %.2f 秒\n", remaining.Seconds())
			time.Sleep(500 * time.Millisecond)
		} else {
			fmt.Printf("[倒计时] 距离抢购还有 %.3f 秒\n", remaining.Seconds())
			time.Sleep(20 * time.Millisecond)
		}
	}
	
	return nil
}

// 等待到目标时间
func waitUntilTargetTime(targetHour, targetMinute, advanceMs int) error {
	jdTimeInfo, err := getJDTime()
	if err != nil {
		return fmt.Errorf("获取京东时间失败: %v", err)
	}
	
	now := jdTimeInfo.ServerTime
	targetTime := time.Date(now.Year(), now.Month(), now.Day(), targetHour, targetMinute, 0, 0, now.Location())
	
	// 如果目标时间已过，设置为明天
	if now.After(targetTime) {
		targetTime = targetTime.Add(24 * time.Hour)
	}
	
	fmt.Printf("京东服务器当前时间: %s\n", now.Format("2006-01-02 15:04:05"))
	fmt.Printf("目标兑换时间: %s\n", targetTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("提前抢购: %d毫秒\n", advanceMs)
	
	return waitUntilJDTime(targetTime, advanceMs)
}

// 单个异步兑换请求
func exchangeItemAsync(ctx context.Context, cookie string, requestID int, wg *sync.WaitGroup, results chan<- bool) {
	defer wg.Done()
	
	// 生成随机UUID和时间戳
	randomUUID := uuid.New().String()
	timestamp := time.Now().UnixMilli()
	
	// 构建请求体
	body := ExchangeBody{
		BusinessType:         118502,
		ActivityID:           810602,
		Quantity:             1,
		Score:                2,
		Remark:               "",
		UUID:                 randomUUID,
		AddrCode:             5404598669,
		ExchangeWareType:     2,
		WareBusinessID:       "1192685891",
		DeliveryMode:         3,
		ActivityWareID:       1180704,
		ParentActivityWareID: nil,
		ParentActivityID:     nil,
		StockType:            1,
		UpperBusinessType:    nil,
		OrderType:            2,
		UserName:             "薛薛薛",
		PhoneNum:             "***********",
		UserAddress:          "广东清远市清城区东城街道象牙岭家和微超",
		ProvinceID:           19,
		CityID:               1704,
		CountyID:             37734,
		TownID:               56600,
		AddressDetail:        "象牙岭家和微超",
		Timestamp:            timestamp,
	}
	
	bodyJSON, _ := json.Marshal(body)
	
	// 构建表单数据
	data := url.Values{
		"appid":         {"h5-sep"},
		"functionId":    {"SEP_EXCHANGE_SUBMIT"},
		"body":          {string(bodyJSON)},
		"client":        {"m"},
		"clientVersion": {"6.0.0"},
	}
	
	req, err := http.NewRequestWithContext(ctx, "POST", EXCHANGE_URL, strings.NewReader(data.Encode()))
	if err != nil {
		fmt.Printf("💥 请求%d 创建失败: %v\n", requestID, err)
		results <- false
		return
	}
	
	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	req.Header.Set("x-referer-page", "https://m-sep.jd.com/Settlement")
	req.Header.Set("x-api-eid-token", "jdd03UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DMAAAAMXPUU2VVAAAAAAC2EI7XUUUTWUOAX")
	req.Header.Set("x-rp-client", "h5_1.0.0")
	req.Header.Set("Origin", "https://m-sep.jd.com")
	req.Header.Set("Referer", "https://m-sep.jd.com/")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Cookie", cookie)
	
	resp, err := httpClient.Do(req)
	if err != nil {
		fmt.Printf("💥 请求%d 异常: %v\n", requestID, err)
		results <- false
		return
	}
	defer resp.Body.Close()
	
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("💥 请求%d 读取响应失败: %v\n", requestID, err)
		results <- false
		return
	}
	
	var result ExchangeResponse
	if err := json.Unmarshal(respBody, &result); err != nil {
		fmt.Printf("💥 请求%d 解析响应失败: %v\n", requestID, err)
		results <- false
		return
	}
	
	fmt.Printf("请求%d: %s\n", requestID, string(respBody))
	
	if result.Success {
		fmt.Printf("🎉 请求%d 兑换成功!\n", requestID)
		results <- true
	} else {
		switch result.ErrorCode {
		case 3011:
			fmt.Printf("❌ 请求%d 库存不足\n", requestID)
		case 3001:
			fmt.Printf("⚠️ 请求%d 重复提交\n", requestID)
		case 9997:
			fmt.Printf("🔐 请求%d 用户未登录，Cookie无效\n", requestID)
		default:
			fmt.Printf("❓ 请求%d 未知错误: %s\n", requestID, result.ErrorMessage)
		}
		results <- false
	}
}

// 批量异步兑换
func batchExchangeAsync(cookie string, concurrentCount int) bool {
	fmt.Printf("🚀 启动 %d 个并发请求...\n", concurrentCount)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var wg sync.WaitGroup
	results := make(chan bool, concurrentCount)

	// 启动并发请求
	for i := 0; i < concurrentCount; i++ {
		wg.Add(1)
		go exchangeItemAsync(ctx, cookie, i+1, &wg, results)
	}

	// 等待所有请求完成
	wg.Wait()
	close(results)

	// 统计结果
	successCount := 0
	for result := range results {
		if result {
			successCount++
		}
	}

	fmt.Printf("📊 批量兑换完成: %d/%d 成功\n", successCount, concurrentCount)
	return successCount > 0
}

// 定时兑换任务
func scheduledExchange(cookie string) {
	for {
		// 等待到早上10点（提前200毫秒抢购）
		if err := waitUntilTargetTime(TARGET_HOUR, TARGET_MINUTE, ADVANCE_MS); err != nil {
			fmt.Printf("等待目标时间失败: %v\n", err)
			time.Sleep(60 * time.Second)
			continue
		}

		fmt.Println(strings.Repeat("=", 50))
		jdTimeInfo, _ := getJDTime()
		fmt.Printf("开始执行定时兑换任务 - %s\n", jdTimeInfo.ServerTime.Format("2006-01-02 15:04:05"))
		fmt.Println(strings.Repeat("=", 50))

		// 执行批量异步兑换（50个并发请求）
		success := batchExchangeAsync(cookie, CONCURRENT_NUM)

		if success {
			fmt.Println("兑换成功！等待明天继续...")
		} else {
			fmt.Println("兑换失败，等待明天重试...")
		}

		fmt.Println(strings.Repeat("=", 50))
		fmt.Println("任务完成，等待下次执行...")
		fmt.Println(strings.Repeat("=", 50))
	}
}

func main() {
	// 内置Cookie
	cookie := "__jda=122270672.1750150929483324107970.1750150929.1750150929.1750150929.1; __jdv=122270672%7Cdirect%7C-%7Cnone%7C-%7C1750150929484; __jdc=122270672; mba_muid=1750150929483324107970; 3AB9D23F7A4B3C9B=UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DM; TrackerID=ALWJ94h4GXYOmMLyPJbNwQ-sWKOSECHJer1KXme55arH0lAMhDWPv77_Z9pwIo2IirZ9SS9StdAJgYmKBZQEnB__9r6W81AhLX2Cr8C8FdsMDoh3tU9vjFGMjvushxoC6XsUQEBYyJjm5GMfx736dw; pt_key=AAJoUS8fADAPTsz35xG0cXOXDl2K8hDdk_EimM1m_m9IFcA-rDrfkmoC_2U52nlgNCsSP5g5EYs; pt_pin=jd_IOPnkWamWXQT; pt_token=my73pjp0; pwdt_id=jd_IOPnkWamWXQT; sfstoken=tk01med791d27a8sMXgyKzN4Mjdyl7UpeBfwdzJvUhVEfuyVg/HET+mZFd/uSjpob7TvY+eOo5mh9fQ0WZW4KdQHhcBi; pt_st=1_rCOGd9aDH7Mvhf4ZRhMzJbDJygk8_TiVv_PtKYWfQ-RanGXYGLD9fRlzvUVEI4pEeVnnCSuM89r8sISmDkm2FpYwAoTqm4fbyqgVVWldLFpEKuvCtEMoWIDB4GmhuoPzEwjL2VUuWT28AYG51sw4lCJMa5xt_nkLE64WRo83n5IhNh4scTDc1dXVEpf0NfGg4EnVPuLctyfX02FKaRWoatphwV2wHoNJnCw1DRf0dCQL; __jdb=122270672.6.1750150929483324107970|1.1750150929; 3AB9D23F7A4B3CSS=jdd03UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DMAAAAMXPU6MDCQAAAAACW7KAAYQ7NWHYIX; _gia_d=1; sepCJSToken=jdd03UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DMAAAAMXPU6MDCQAAAAACW7KAAYQ7NWHYIX; mba_sid=17501509294851566716052.14; sdtoken=AAbEsBpEIOVjqTAKCQtvQu17yH_EqEN7rcjtUJnNVPDFJHaNZZ1lHjTUtLj4WvQcnX8XpM5UQU0IM00ub61xnIHym5x_2B6L-8p1ym332oV2h8sVHBwREoIrpdCtzDlE82o; __jd_ref_cls=JDSEP_SubmitExchange"

	fmt.Println("京东商品定时兑换脚本启动（Go版本 - 基于京东服务器时间）")
	fmt.Printf("每天早上%02d:%02d自动执行兑换任务\n", TARGET_HOUR, TARGET_MINUTE)
	fmt.Printf("支持提前%d毫秒抢购\n", ADVANCE_MS)
	fmt.Printf("并发%d个请求提高成功率\n", CONCURRENT_NUM)
	fmt.Println("按 Ctrl+C 停止脚本")
	fmt.Println(strings.Repeat("-", 50))

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动定时任务
	go scheduledExchange(cookie)

	// 等待退出信号
	<-sigChan
	fmt.Println("\n脚本已停止")
}
