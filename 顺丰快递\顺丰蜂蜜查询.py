import requests
import json
from urllib.parse import unquote, quote
import warnings
from urllib3.exceptions import InsecureRequestWarning
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=ResourceWarning)
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=ImportWarning)
warnings.filterwarnings("ignore", category=PendingDeprecationWarning)
warnings.filterwarnings("ignore", category=RuntimeWarning)
warnings.filterwarnings("ignore", category=SyntaxWarning)
warnings.filterwarnings("ignore", category=UnicodeWarning)
warnings.filterwarnings("ignore", category=InsecureRequestWarning)

# ====== 自动登录获取sessionId工具函数 ======
def sf_login(sfurl):
    s = requests.session()
    s.verify = False
    headers = {
        'Host': 'mcs-mimp-web.sf-express.com',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090551) XWEB/6945 Flue',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'sec-fetch-site': 'none',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-user': '?1',
        'sec-fetch-dest': 'document',
        'accept-language': 'zh-CN,zh',
        'platform': 'MINI_PROGRAM',
    }
    # url编码
    if '%' not in sfurl:
        base_url, params = sfurl.split('?', 1)
        encoded_params = quote(params, safe='&=')
        sfurl = f"{base_url}?{encoded_params}"
    res = s.get(sfurl, headers=headers)
    cookies = s.cookies.get_dict()
    sessionId = cookies.get('sessionId', '')
    user_id = cookies.get('_login_user_id_', '')
    phone = cookies.get('_login_mobile_', '')
    if sessionId:
        # print(f"用户:【{phone[:3]}****{phone[7:]}】登陆成功，sessionId={sessionId}")
        return sessionId, cookies
    else:
        # print("获取用户信息失败")
        return '', {}

# ====== 配置区：支持多账号自动登录 ======
urls = '''
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/app/activityRedirect?sign=AzisPZ5zUnFUwZDnlK40jizj%2FTrfONf7necWqmlwmSvjXiI0%2BrKA30jqriZwvzDO%2FOzy%2BDphbtva7V%2BrHNtLgODCswlHurpOWZThydaepBbip0EQEak2LN2l5Zi%2B5QDj86JX6EVWCLRJlB5AagcfY%2FyvemoBRHwq%2Ff0beRyyZXGc8X1vds59VvYotqvA2bku4vzYgEEBayJsoMk4C%2F6ki%2F6KBme4e7PjMATN5nxX7ahs%2FO8T%2FH14ji1yXGFUr6mBLqrVAA2FxMZ9Okp%2FS5KbTkFXcB6kid4f%2F3JvC4G7%2Br3%2Bd61%2Fda%2BQAOX3VIgFjsJ95nkG6fDsnycOGzzOk5nGsA%3D%3D&encryptionType=base64&source=SFAPP&bizCode=eyJsaW5rQ29kZSI6IlNGQUMyMDI1MDUxMjEwMTQwMTk4MiIsImZyb20iOiIyNWR3YXBwdHkiLCJzdXBwb3J0U2hhcmUiOiJZRVMiLCJwYXRoIjoiL29yaWdpbi9hL21pbXAtYWN0aXZpdHkvZHJhZ29uQm9hdDIwMjUiLCJpbnZpdGVVc2VySWQiOiIyMTgyMTNCREM5NUM0QkIxOERBNjc3OTRGRjgwNkE5NSIsImludml0ZU1vYmlsZSI6IjE3MSoqKio0Mjk2IiwidHlwZSI6InRpZXRpZSIsInRva2VuIjoiNTkzMDM1ODg4ZjY2NDM0Zjg3ZDBmZTUyNjNjODVlNjciLCJpbnZpdGVEZXZpY2VGcCI6IkRVYmd0QUJuNzJveXRNYTdxcERDZV80SklyRkQyMFNSaWg5ZiIsImludml0ZUVuY3J5SXAiOiJHWWRycUVFa3Z5Q2JsaFlsQXl4M2NnJTNEJTNEIn0=&citycode=763&cityname=%E6%B8%85%E8%BF%9C
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/app/activityRedirect?sign=gTuptDKdpjGJ53%2FcxDazRBe%2BAKJmqLNMcAxfCHoL5L681ZI6hcyFeNrmo2K7Xsirpyv4oX6Cx8Fe4vyPMK3DYqxiF8QSHi%2BqR7XgPUvf22jjPqtCmat8T4zMkJr39aE%2FkSwHQPvTbKcyf3nVOWjHYTSGMUZbR8UIZA0MMH8zKTFJQE%2BBwUR2KVuhDFSk%2FbHowoep8D5L2y%2FGYqkGIYxDw8e2bxF2%2BckMWhicBttXv%2FXHmPl2isirumls51CmhLZME0PSNufPTvIzX8mojJaAQOx9fzbUq0i6NQZiUnqjD%2BapkkC0nF3iETyS2NvI90u4xF6vVJzQKEyzxgIMm%2FYUsw%3D%3D&source=SFAPP&bizCode=%7B%22path%22%3A%22%2Fup%2Dmember%2FnewPoints%22%2C%22linkCode%22%3A%22SFAC20230803190840424%22%2C%22supportShare%22%3A%22YES%22%2C%22subCategoryCode%22%3A%221%22%2C%22from%22%3A%22point240613%22%2C%22categoryCode%22%3A%221%22%7D
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/weChat/activityRedirect?source=CX&unionId=B%2BhiWHIhJN0cD3Q0Mpd7eP4FCIKNZM9eoJnNFKIst3k%3D&openId=lcKFOxkPXJhWAnZsk3bLcZOF7cAY7qC9A2ABAiA3gWc%3D&memId=Onnbx%2F9plfexp0kDn4u8u19ymnF%2BKVKOdU3FkfaBTkADdjSr%2F6X0WoiZtgGzs7sG&memNo=6tB15T6k0ZvyroDMcHJm6TdkjdQgRXmIAGt0XmIEiNcDdjSr%2F6X0WoiZtgGzs7sG&mobile=cuWgYYC4BFLz1UeYJ8d6jA%3D%3D&mediaCode=CXWX&bizCode=%7B%22path%22%3A%22%2Fup-member%2FnewHome%22%2C%22linkCode%22%3A%22SFAC20230629183611255%22%2C%22supportShare%22%3A%22YES%22%2C%22from%22%3A%22ylhyzx23%22%7D
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/weChat/activityRedirect?source=CX&unionId=99wflsbqzVBFLJijk0ZXFt6mRPCxPvYy4r4t6l4Ik3c%3D&openId=05YZUIkqIWGW2VSI1R7YN7infHO7ylotSnYqhq4Q4X4%3D&memId=WhsIj%2BYA%2BkWWobkk2s7bgfYXMbarklENUnHzFx3aSIIDdjSr%2F6X0WoiZtgGzs7sG&memNo=6tB15T6k0ZvyroDMcHJm6ZpAEjp%2FDyxG4PgjOChT54UDdjSr%2F6X0WoiZtgGzs7sG&mobile=kM3o98nl6H2HL6o%2FROmY9A%3D%3D&mediaCode=CXWX&bizCode=%7B%22path%22%3A%22%2Fup-member%2FnewHome%22%2C%22linkCode%22%3A%22SFAC20230629183611255%22%2C%22supportShare%22%3A%22YES%22%2C%22from%22%3A%22ylhyzx23%22%7D
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/app/shareRedirect?sign=sn0VSFUTkGJyexcOTEAJtXOruLDkpVP7NvHqBYIhnCkQiPKHMn0Yu5GCnfYl%2FBmQ87iJ%2F9KU%2FSfMk1fqvGyV9ix%2BMh2Eh6YZH%2F2gSL13Vbx%2B2LEGVyJZh19iqkWFUohenH0DrfsQ8t29XQ%2BtQuyIwUSWbgksbIvRjIp2Nqoz2RlcxhnDlo8vBGBZZlKY%2Bcmx899SWVVIASweJKG0MkvloS0Wkq4AcNBRWbCADAjQvkmRISxRh%2F2KWDuC47AORW2Re5gGYl5rtVHrhq3E5FSel2OvY6Ef1O1YkU7sHzJAXQQHjPJbEDhbDZvDsF1q49tQ3H3qCWAuvNmMWTM7SfsnAQ%3D%3D&source=SFAPP&bizCode=619
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/app/shareRedirect?sign=YiUQyzERb0BL9fgAUFtuyinD5sRPUtJ6KgRDLZ%2FpP6ksZqOb8%2B%2B%2B2lqwik2iyoe0cdmI8LJIIjY57ftT7Bt%2BUG%2B9765q5SRkILUDfbn1QrWlMR1f5ox5tPs1zPcUd%2FiERvtWxnbZwDMNatb7Fn%2Fy%2BJqJG9hGknjduq5zKrChLgHHL199JMaKys7fT8WQqEixes6r%2B8KvutjDFSgd%2BGwFBVuLYNsBirgbKtsmO4wZkels8MAW1HLoQreffbcDaGgMlMA0ob%2BvNrNAGfaONvUBOhxGhPWcPHt8FB7uXF7IvBglZKn9bUO9InOD%2FOw85OLi7%2FLNDDrWmih9qHIuwaQCdg%3D%3D&source=SFAPP&bizCode=619
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/app/shareRedirect?sign=n%2B%2BPYZyODoh8iak7c79zdqRggq0BqERIsVALpuYH%2FnXyXLYSWnVcYbtZ9rya6ML7XP9oYs5iAMMo1U5qdtKELBPFn2sAVKnT%2FTXjEWillqgyyWuXkZ4Xlt6TCIHVxA41Mfesc2UaFrvj6XcUJdtfOv%2Bonf%2FIAZzbRp7Q4pt3KH8%2F%2FwnGQRZWPYxL3oM80qrh8bKnlF9gEIKZlsGbzWDLEFODkYgXR4Jz%2FdyTUmy5U4tEZ4f%2BaWi8cGgXdqwqyfExmb4qbJKMjRxSRmZvkseRZ1CgjbDZJtEVjrr%2F8h1QB1VoST4bVROsqTwsit7ugeIVg5%2BpstwI9%2BBoqV0YusKCig%3D%3D&source=SFAPP&bizCode=619
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/weChat/shareGiftReceiveRedirect?source=CX&scene=6&unionId=Wc6ry3I0oqMuE8ajFJqygo24q%2BJF9hUnxcn1il50i4I%3D&openId=7KUpy9o2FeKm0Xs43chlKiX0JbSIyZwTqBWfHVfLpnk%3D&memId=GTj7AKVqiV0Fh94sRl5D7P9W%2Fh0ubxGAaBDd2X6EjqsDdjSr%2F6X0WoiZtgGzs7sG&memNo=6tB15T6k0ZvyroDMcHJm6ap%2BZVxhDM91ZMooUXoFs9kDdjSr%2F6X0WoiZtgGzs7sG&mobile=Iaws8gZ56c1lW07U36F4bA%3D%3D&bizCode=619%40%40R0VaTmhYNnZHWHNKZTJLejlPV3hpeDhveGVTMXBzbGhXU0JtR00ySVowQT0%3D
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/app/activityRedirect?sign=rIcwchP3I056StREZ9lNe6f%2BoCSCOvbuWvdRfPQjCkcKJ%2FWFDTRzw9gdYQ0VanHVUYSrQKUC65bRIALckiUXS7acR7mRqfaOD2DZ5fRcoXk1HRduApKvq%2FwZ3vMbOCRaG3bugDgMFHEFm3lhiv2Im%2BnoM9iAk9ox0l3%2Ba9RPGrmmhXQK1tSIcnnltnzGypBPMwuTXRIzRxbVN1J4YmfFmvEzVIs7J9SOPbKwBUHq2J4b63stoq%2FGeF%2FHsR%2F%2F1nGrr67uE%2FsjikPwj%2F5nm0NakE8RN%2BYNNt%2FkthaXf0YQZr%2Fra3m41oZkJMi1dgXcnvy9y%2BhW%2BTP3iNsDPz67ITb4PQ%3D%3D&source=SFAPP&bizCode=%7B%22path%22%3A%22%2Fup%2Dmember%2FnewPoints%22%2C%22linkCode%22%3A%22SFAC20230803190840424%22%2C%22supportShare%22%3A%22YES%22%2C%22subCategoryCode%22%3A%221%22%2C%22from%22%3A%22point240613%22%2C%22categoryCode%22%3A%221%22%7D
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/app/activityRedirect?sign=QHWnVXOKSP1QqXi3QNlSfRLHCVX2GEe%2BJ9whK1%2B%2BMRmRK9NbovI%2BuLmWMiffCTXEwrqGu9NoXriD6Zj%2BBPodREvBjs85KgQphhfTWcGa9dwabHWpcr0mcL1J%2BdKmTuU36svK8fHTdvKJPFf5MQ72PqrYuhXpSHEGsQwWB349w5lQU0L%2F0su%2FRsN55q5z%2B%2FrJBauQbl9hnF0YRffAwRoJBzJHrCTAQBw8naLcrH8z07bat25XOlBbILj7fAOpmwyktvZH%2F%2FGsbJB1s1q%2BXbnmVnvVZPII7CW%2BGMBBKE%2FlOY2hwjNDztrZk35WwXlWH90crrVsktWwzGuVDI2LEo1ZTQ%3D%3D&source=SFAPP&bizCode=%7B%22path%22%3A%22%2Fup%2Dmember%2FnewPoints%22%2C%22linkCode%22%3A%22SFAC20230803190840424%22%2C%22supportShare%22%3A%22YES%22%2C%22subCategoryCode%22%3A%221%22%2C%22from%22%3A%22point240613%22%2C%22categoryCode%22%3A%221%22%7D
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/app/activityRedirect?sign=jxRlMvZUdIOrAJ5novw3h0PjcYARHHmiixANz%2FQQeECyKcuXiGXesdbek5PgXyiCK3drYjc9l8nsMl7vRswy4kSWx3Q3dJs4fxSYSoDQ7kDbHuYHMcxJjRMz9%2B9O2MxBbkXZFsxtvG7Ag%2B0K%2Bf8vE0i%2F1Uw0bOe6r91xDuVbN2nCN3d4bbJI1zbF4fOFe1j4wo7UKE1OG1qAu56k%2Fdb0loxp2sKTYnamYHksQ3KY5BMssrJn1aLY3ASj95VN5Wtukn5dCEC%2B5R1OFoYwWwUxia12EcH9DOg2T5%2FkTLgs2FYMYkq8lP%2F4rfkKh%2FaEBSliKsc5%2BUXwM6hbOlzLFXAFDA%3D%3D&source=SFAPP&bizCode=%7B%22path%22%3A%22%2Fup%2Dmember%2FnewPoints%22%2C%22linkCode%22%3A%22SFAC20230803190840424%22%2C%22supportShare%22%3A%22YES%22%2C%22subCategoryCode%22%3A%221%22%2C%22from%22%3A%22point240613%22%2C%22categoryCode%22%3A%221%22%7D
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/app/activityRedirect?sign=P%2Fi9wRX8BLvuzM3ol7DjVgqXpAO5nH8i%2Fp%2FpfWP8hmFP9evyT1ZGrdeToGsETi2AZ7gQYWiJ9SqRWPEli9JPiHPIdJtktPZHBU%2BpJEN1g7z6E2Uhwb02DUuK4VgffQboqaJDY1Vi49CbUHJzJnvMmJaJHFt9Ui6jY%2FGEmDlRv4u6mg301gxZTzVCDXo6xVTFPOIztjv%2BvSzCSvmDpoYW3U0AVL4KAuXRXMz%2BtcMSA5B5HEawwyQNXX1JnVeU0UxfeK5dLuKFDG8NF4kmoYRHCdn1opQQ8LLZxwuDg2uEnks%2FyM5OHqqhZAcnzh1%2FexkO9Y7pNwHNN%2BgNaIaoz9JksQ%3D%3D&source=SFAPP&bizCode=%7B%22path%22%3A%22%2Fup%2Dmember%2FnewPoints%22%2C%22linkCode%22%3A%22SFAC20230803190840424%22%2C%22supportShare%22%3A%22YES%22%2C%22subCategoryCode%22%3A%221%22%2C%22from%22%3A%22point240613%22%2C%22categoryCode%22%3A%221%22%7D
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/app/activityRedirect?sign=zrO9m%2F9GEjFHoMm9v6wpoY8g5taYzoWUsAlSWAMzydNt4rbv2knINKiPUTJxv0QEXuUsG8S64kpCmSWn%2FSltDh34FvTDX8lYXC0JLKcAKqfQ81VkesZQWDWYdEP%2B4dVp0JOQefHzGA8a3OWTRg8%2BIfDCaXBc88in2rov3I4%2BkT8d46etq4J8fVLDv5FX%2B7o6UPfu%2Ba2xz7REtHAzWvzJ4neHmcVj2wLsrZ43SV6v01DQKkUThEC8kHdTYCjW0j1RVksbycRpdEsNP2SJrF8QaCRhHf6FQ47hyXrLGGmw55stB4zNDBnhIDlYCgZXC0l5f8oXEs2iIzeT%2Fxvzr7YCEw%3D%3D&source=SFAPP&bizCode=%7B%22path%22%3A%22%2Fup%2Dmember%2FnewPoints%22%2C%22linkCode%22%3A%22SFAC20230803190840424%22%2C%22supportShare%22%3A%22YES%22%2C%22subCategoryCode%22%3A%221%22%2C%22from%22%3A%22point240613%22%2C%22categoryCode%22%3A%221%22%7D
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/app/activityRedirect?sign=Kd0a2oGXx%2BhvOb2VsA%2FkDnl0mTtO5VDriJRgKq%2BJwcps0W3%2BDlzEVPxXG%2BZPIeG9iu1pwMRI67pu2IDYnzPtSAF4qpHk2cllqBzl6JKDrNAH6tF3NlzVSCAWVJImx0HdNQTno2LyxDUK66mFE5PB6PHpv2lB64zDCDsAXoUNv2rxPrnjEuditGZ9QgX1TiDYci49VXcywy2JQAVofHYPLZoPEGi94QQqm4k%2BMbvOEYHO9lR0KGnvqxDJXGOGOJoO2zYmzVltfCiLfU4%2BQlG9a8RRPQBfhY5iMm9atdlRlP0RwID%2FDptGp6GdkE5txWFv2%2F1Mlf8mlcN59tM5a7MlZw%3D%3D&source=SFAPP&bizCode=%7B%22path%22%3A%22%2Fup%2Dmember%2FnewPoints%22%2C%22linkCode%22%3A%22SFAC20230803190840424%22%2C%22supportShare%22%3A%22YES%22%2C%22subCategoryCode%22%3A%221%22%2C%22from%22%3A%22point240613%22%2C%22categoryCode%22%3A%221%22%7D
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/app/activityRedirect?sign=X6Px7BFkHdfc4daTCoJ3dA6hYb26gjkdkkcyZcOVJMnU0ClzRiIwKI65KZfrGzNekuBqmIgeJSYLli83U0k1%2FMhS9YJNGyAKuTJIkwL5nVpwi9sEKlTHRqQjhCpXA3sp3Elp86ym6zNkZE7caPjXynk2fZSBHDKQubYr%2FlrYoBZeCoSG%2F7jFPHwJKrsrmcNnsi%2Fgk393Epc2PhZDiq8IRe1yrLbO6g2bxJixS1oOl%2FWyNOCNoHLqqxaXJzgJTy1xqkvRzWf3UnK0ydg4KnGt4G2sJbAKSbFdGXxWnjA%2FfCeKsH1xgE%2FtradxEFxCjp5Io0z8ctUi9AMAZAHQlu%2Fd6A%3D%3D&source=SFAPP&bizCode=%7B%22path%22%3A%22%2Fup%2Dmember%2FnewPoints%22%2C%22linkCode%22%3A%22SFAC20230803190840424%22%2C%22supportShare%22%3A%22YES%22%2C%22subCategoryCode%22%3A%221%22%2C%22from%22%3A%22point240613%22%2C%22categoryCode%22%3A%221%22%7D
https://mcs-mimp-web.sf-express.com/mcs-mimp/share/weChat/shareGiftReceiveRedirect?source=CX&scene=6&unionId=c4Wnav41wq5Bca3e3dSHKJYExRkLoln%2BaIADXeDEwGg%3D&openId=bu4E01vFo5zcOEpmq6a%2FT2Z%2B2Ob6HAlD52GypyM%2Bh9E%3D&memId=Qnu%2BTj9WvS9HhHSDxRYRKJyJHnUn5irtfRk8npFPy20DdjSr%2F6X0WoiZtgGzs7sG&memNo=6tB15T6k0ZvyroDMcHJm6Xq89%2FjODlQ2KwvN%2B4V7%2BOIDdjSr%2F6X0WoiZtgGzs7sG&mobile=NHUEdOdvPE7dYYVaFIvvKQ%3D%3D&bizCode=619%40%40R0VaTmhYNnZHWHNKZTJLejlPV3hpeDhveGVTMXBzbGhXU0JtR00ySVowQT0%3D
# 在此处每行填写一个顺丰分享链接，支持注释和空行
'''.strip().split('\n')
urls = [u.strip() for u in urls if u.strip() and not u.strip().startswith('#')]
#CSESSIONS = []
COOKIES = []
if urls:
    for u in urls:
        sid, cookies = sf_login(u)
        if sid:
            #CSESSIONS.append(f'sessionId={sid}')
            COOKIES.append(cookies)

url = "https://mcs-mimp-web.sf-express.com/mcs-mimp/commonPost/~memberNonactivity~receiveExchangeIndexService~indexData"

payload = {}

for idx, cookies in enumerate(COOKIES):
    phone = cookies.get('_login_mobile_', '')
    phone_masked = phone[:3] + '****' + phone[7:] if len(phone) == 11 else phone
    cookie_str = "; ".join([f"{k}={v}" for k, v in cookies.items()])
    headers = {
     'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c33)XWEB/13639",
     'Accept': "application/json, text/plain, */*",
     'Content-Type': "application/json",
     'syscode': "MCS-MIMP-CORE",
     'content-type': "application/json;charset=UTF-8",
     'timestamp': "1750058650034",
     'signature': "d87bc50bfbd8f4d98c8e599d23b0b920",
     'platform': "MINI_PROGRAM",
     'channel': "wxwdsj",
     'sw8': "1-NTMzNGIyOWMtMDQ2Ni00MjZmLWFjOGYtODY0ZTk1NmRiNTI0-NjMxOWVmNGMtYTM1ZS00MWI3LTkwMDEtNWZmZjk4YzhiNjJi-0-ZmI0MDgxNzA4NWJlNGUzOThlMGI2ZjRiMDgxNzc3NDY=-d2Vi-L2luYm94UHJlc2VudA==-L21jcy1taW1wL2NvbW1vblBvc3Qvfm1lbWJlck5vbmFjdGl2aXR5fnJlY2VpdmVFeGNoYW5nZUluZGV4U2VydmljZX5pbmRleERhdGE=",
     'origin': "https://mcs-mimp-web.sf-express.com",
     'sec-fetch-site': "same-origin",
     'sec-fetch-mode': "cors",
     'sec-fetch-dest': "empty",
     'referer': "https://mcs-mimp-web.sf-express.com/inboxPresent?mobile=135****2814&userId=********************************&path=/inboxPresent&linkCode=SFAC20230323145721922&supportShare=YES&from=wxwdsj&citycode=763&cityname=%E6%B8%85%E8%BF%9C",
     'accept-language': "zh-CN,zh;q=0.9",
     'priority': "u=1, i",
     'Cookie': cookie_str
      }

    response = requests.post(url, data=json.dumps(payload), headers=headers)
    print(f"账号 {idx+1} ({phone_masked}) 响应: {response.text}")