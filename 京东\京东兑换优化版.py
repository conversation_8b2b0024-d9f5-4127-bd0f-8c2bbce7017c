import json
import time
import random
import uuid
import requests
import datetime
import threading

def generate_uuid():
    """生成随机UUID"""
    return str(uuid.uuid4())

def get_current_time():
    """获取当前时间戳"""
    return int(time.time() * 1000)

def exchange_item(cookie, retry=3, delay=0.2):
    """尝试兑换商品
    
    Args:
        cookie: 用户Cookie
        retry: 重试次数
        delay: 请求间隔(秒)
    """
    url = "https://api.m.jd.com/api?functionId=SEP_EXCHANGE_SUBMIT"
    
    # 提取Cookie中的关键信息
    pt_pin = ""
    for item in cookie.split(';'):
        if 'pt_pin=' in item:
            pt_pin = item.strip()
            break
    
    print(f"当前账号: {pt_pin}")
    
    for attempt in range(retry):
        # 生成随机UUID和时间戳
        random_uuid = generate_uuid()
        timestamp = get_current_time()
        
        payload = {
            'appid': "h5-sep",
            'functionId': "SEP_EXCHANGE_SUBMIT",
            'body': json.dumps({
                "businessType": 118502,
                "activityId": 810602,
                "quantity": 1,
                "score": 2,
                "remark": "",
                "uuid": random_uuid,
                "addrCode": 5404598669,
                "exchangeWareType": 2,
                "wareBusinessId": "1192685891",
                "deliveryMode": 3,
                "activityWareId": 1180704,
                "parentActivityWareId": None,
                "parentActivityId": None,
                "stockType": 1,
                "upperBusinessType": None,
                "orderType": 2,
                "userName": "薛薛薛",
                "phoneNum": "***********",
                "userAddress": "广东清远市清城区东城街道象牙岭家和微超",
                "provinceId": 19,
                "cityId": 1704,
                "countyId": 37734,
                "townId": 56600,
                "addressDetail": "象牙岭家和微超",
                "_t": timestamp  # 添加时间戳
            }),
            'client': "m",
            'clientVersion': "6.0.0"
        }
        
        headers = {
            'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            'x-referer-page': "https://m-sep.jd.com/Settlement",
            'x-api-eid-token': "jdd03UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DMAAAAMXPUU2VVAAAAAAC2EI7XUUUTWUOAX",
            'x-rp-client': "h5_1.0.0",
            'origin': "https://m-sep.jd.com",
            'referer': "https://m-sep.jd.com/",
            'Cookie': cookie
        }
        
        try:
            print(f"第{attempt+1}次尝试兑换，UUID: {random_uuid[:8]}...")
            response = requests.post(url, data=payload, headers=headers)
            result = response.json()
            
            print(f"响应: {json.dumps(result, ensure_ascii=False)}")
            
            if result.get('success'):
                print("兑换成功!")
                return True
            elif result.get('errorCode') == 3011:
                print("库存不足，等待补货...")
            elif result.get('errorCode') == 3001:
                print("重复提交，尝试更换参数...")
                # 增加随机延迟
                time.sleep(random.uniform(0.01, 0.3)) # 调整重复提交的延迟范围
            elif result.get('errorCode') == 9997 and result.get('errorMsg') == "用户未登录":
                print("错误: 用户未登录。请更新您的Cookie！")
                return False # 停止重试，因为Cookie无效
            else:
                print(f"未知错误: {result.get('errorMsg')}")
            
            # 请求间隔
            if attempt < retry - 0.2:
                delay_time = delay + random.uniform(0.01, 0.3) # 调整每次重试的延迟范围
                print(f"等待 {delay_time:.2f} 秒后重试...")
                time.sleep(delay_time)
                
        except Exception as e:
            print(f"请求异常: {str(e)}")
            time.sleep(delay)
    
    return False

def wait_until_target_time(target_hour=10, target_minute=0):
    """等待到指定时间"""
    while True:
        now = datetime.datetime.now()
        target_time = now.replace(hour=target_hour, minute=target_minute, second=0, microsecond=0)

        # 如果目标时间已过，设置为明天的目标时间
        if now >= target_time:
            target_time += datetime.timedelta(days=1)

        wait_seconds = (target_time - now).total_seconds()
        print(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"目标时间: {target_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"等待 {wait_seconds:.0f} 秒后开始兑换...")

        time.sleep(wait_seconds)
        break

def scheduled_exchange():
    """定时兑换任务"""
    # 内置Cookie
    cookie = "__jda=122270672.1750150929483324107970.1750150929.1750150929.1750150929.1; __jdv=122270672%7Cdirect%7C-%7Cnone%7C-%7C1750150929484; __jdc=122270672; mba_muid=1750150929483324107970; 3AB9D23F7A4B3C9B=UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DM; TrackerID=ALWJ94h4GXYOmMLyPJbNwQ-sWKOSECHJer1KXme55arH0lAMhDWPv77_Z9pwIo2IirZ9SS9StdAJgYmKBZQEnB__9r6W81AhLX2Cr8C8FdsMDoh3tU9vjFGMjvushxoC6XsUQEBYyJjm5GMfx736dw; pt_key=AAJoUS8fADAPTsz35xG0cXOXDl2K8hDdk_EimM1m_m9IFcA-rDrfkmoC_2U52nlgNCsSP5g5EYs; pt_pin=jd_IOPnkWamWXQT; pt_token=my73pjp0; pwdt_id=jd_IOPnkWamWXQT; sfstoken=tk01med791d27a8sMXgyKzN4Mjdyl7UpeBfwdzJvUhVEfuyVg/HET+mZFd/uSjpob7TvY+eOo5mh9fQ0WZW4KdQHhcBi; pt_st=1_rCOGd9aDH7Mvhf4ZRhMzJbDJygk8_TiVv_PtKYWfQ-RanGXYGLD9fRlzvUVEI4pEeVnnCSuM89r8sISmDkm2FpYwAoTqm4fbyqgVVWldLFpEKuvCtEMoWIDB4GmhuoPzEwjL2VUuWT28AYG51sw4lCJMa5xt_nkLE64WRo83n5IhNh4scTDc1dXVEpf0NfGg4EnVPuLctyfX02FKaRWoatphwV2wHoNJnCw1DRf0dCQL; __jdb=122270672.6.1750150929483324107970|1.1750150929; 3AB9D23F7A4B3CSS=jdd03UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DMAAAAMXPU6MDCQAAAAACW7KAAYQ7NWHYIX; _gia_d=1; sepCJSToken=jdd03UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DMAAAAMXPU6MDCQAAAAACW7KAAYQ7NWHYIX; mba_sid=17501509294851566716052.14; sdtoken=AAbEsBpEIOVjqTAKCQtvQu17yH_EqEN7rcjtUJnNVPDFJHaNZZ1lHjTUtLj4WvQcnX8XpM5UQU0IM00ub61xnIHym5x_2B6L-8p1ym332oV2h8sVHBwREoIrpdCtzDlE82o; __jd_ref_cls=JDSEP_SubmitExchange"

    while True:
        # 等待到早上10点
        wait_until_target_time(10, 0)

        print("=" * 50)
        print(f"开始执行定时兑换任务 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)

        # 执行兑换
        success = exchange_item(cookie, retry=10, delay=0.1)

        if success:
            print("兑换成功！等待明天继续...")
        else:
            print("兑换失败，等待明天重试...")

        print("=" * 50)
        print("任务完成，等待下次执行...")
        print("=" * 50)

# 使用示例
if __name__ == "__main__":
    print("京东商品定时兑换脚本启动")
    print("每天早上10:00自动执行兑换任务")
    print("按 Ctrl+C 停止脚本")
    print("-" * 50)

    try:
        # 启动定时任务
        scheduled_exchange()
    except KeyboardInterrupt:
        print("\n脚本已停止")
    except Exception as e:
        print(f"脚本运行异常: {str(e)}")
