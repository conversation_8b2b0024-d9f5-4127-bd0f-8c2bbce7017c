import json
import time
import random
import uuid
import requests
import datetime
import threading
import aiohttp
import asyncio
import ssl

def generate_uuid():
    """生成随机UUID"""
    return str(uuid.uuid4())

def get_current_time():
    """获取当前时间戳"""
    return int(time.time() * 1000)

async def get_jd_time():
    """获取京东服务器时间（异步请求）"""
    jd_time_url = "https://api.m.jd.com/client.action?functionId=queryMaterialProducts&client=wh5"
    try:
        async with aiohttp.ClientSession() as session:
            request_start = time.time()
            async with session.head(jd_time_url, timeout=3) as response:
                request_end = time.time()
                # 计算网络延迟
                network_delay = (request_end - request_start) / 2

                # 从响应头中提取服务器时间（UTC+8）
                server_time_str = response.headers.get('Date', '')
                if server_time_str:
                    server_time = datetime.datetime.strptime(
                        server_time_str, '%a, %d %b %Y %H:%M:%S GMT'
                    ).timestamp() + 28800 + network_delay  # GMT+8时区校准 + 网络延迟补偿

                    local_time = time.time()
                    time_diff = server_time - local_time  # 服务器时间 - 本地时间

                    print(f"京东服务器时间: {datetime.datetime.fromtimestamp(server_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
                    print(f"本地系统时间: {datetime.datetime.fromtimestamp(local_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
                    print(f"网络延迟: {network_delay:.3f}秒")
                    print(f"时间差异: {time_diff:.3f}秒 (正数表示服务器时间快)")

                    return server_time, time_diff
                else:
                    raise Exception("无法获取服务器时间头")

    except Exception as e:
        print(f"获取京东时间失败: {e}")
        local_time = time.time()
        print(f"使用本地时间: {datetime.datetime.fromtimestamp(local_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        return local_time, 0.0  # 降级为本地时间，时间差为0

async def wait_until_jd_time(target_jd_time, advance_ms=200):
    """等待至京东服务器时间到达目标时刻（支持提前抢购）

    Args:
        target_jd_time: 目标时间戳
        advance_ms: 提前毫秒数，默认200毫秒
    """
    # 计算提前时间
    advance_seconds = advance_ms / 1000.0
    target_time_advanced = target_jd_time - advance_seconds

    print(f"提前抢购设置: {advance_ms}毫秒")

    while True:
        current_jd_time, time_diff = await get_jd_time()  # 获取京东时间和时间差
        remaining = target_time_advanced - current_jd_time

        if remaining <= 0:
            print(f"[时间同步] 提前 {advance_ms}ms 开始抢购！")
            break

        # 显示倒计时
        if remaining > 5:
            print(f"[倒计时] 距离抢购还有 {remaining:.1f} 秒")
        elif remaining > 1:
            print(f"[倒计时] 距离抢购还有 {remaining:.2f} 秒")
        else:
            print(f"[倒计时] 距离抢购还有 {remaining:.3f} 秒")

        # 动态调整等待间隔（平衡请求频率和精度）
        if remaining > 5.0:
            await asyncio.sleep(1.0)
        elif remaining > 1.0:
            await asyncio.sleep(min(remaining / 2, 0.5))
        elif remaining > 0.1:
            await asyncio.sleep(0.02)
        else:
            await asyncio.sleep(0.001)

def exchange_item(cookie, retry=3, delay=0.2):
    """尝试兑换商品
    
    Args:
        cookie: 用户Cookie
        retry: 重试次数
        delay: 请求间隔(秒)
    """
    url = "https://api.m.jd.com/api?functionId=SEP_EXCHANGE_SUBMIT"
    
    # 提取Cookie中的关键信息
    pt_pin = ""
    for item in cookie.split(';'):
        if 'pt_pin=' in item:
            pt_pin = item.strip()
            break
    
    print(f"当前账号: {pt_pin}")
    
    for attempt in range(retry):
        # 生成随机UUID和时间戳
        random_uuid = generate_uuid()
        timestamp = get_current_time()
        
        payload = {
            'appid': "h5-sep",
            'functionId': "SEP_EXCHANGE_SUBMIT",
            'body': json.dumps({
                "businessType": 118502,
                "activityId": 810602,
                "quantity": 1,
                "score": 2,
                "remark": "",
                "uuid": random_uuid,
                "addrCode": 5404598669,
                "exchangeWareType": 2,
                "wareBusinessId": "1192685891",
                "deliveryMode": 3,
                "activityWareId": 1180704,
                "parentActivityWareId": None,
                "parentActivityId": None,
                "stockType": 1,
                "upperBusinessType": None,
                "orderType": 2,
                "userName": "薛薛薛",
                "phoneNum": "***********",
                "userAddress": "广东清远市清城区东城街道象牙岭家和微超",
                "provinceId": 19,
                "cityId": 1704,
                "countyId": 37734,
                "townId": 56600,
                "addressDetail": "象牙岭家和微超",
                "_t": timestamp  # 添加时间戳
            }),
            'client': "m",
            'clientVersion': "6.0.0"
        }
        
        headers = {
            'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            'x-referer-page': "https://m-sep.jd.com/Settlement",
            'x-api-eid-token': "jdd03UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DMAAAAMXPUU2VVAAAAAAC2EI7XUUUTWUOAX",
            'x-rp-client': "h5_1.0.0",
            'origin': "https://m-sep.jd.com",
            'referer': "https://m-sep.jd.com/",
            'Cookie': cookie
        }
        
        try:
            print(f"第{attempt+1}次尝试兑换，UUID: {random_uuid[:8]}...")
            response = requests.post(url, data=payload, headers=headers)
            result = response.json()
            
            print(f"响应: {json.dumps(result, ensure_ascii=False)}")
            
            if result.get('success'):
                print("兑换成功!")
                return True
            elif result.get('errorCode') == 3011:
                print("库存不足，等待补货...")
            elif result.get('errorCode') == 3001:
                print("重复提交，尝试更换参数...")
                # 增加随机延迟
                time.sleep(random.uniform(0.01, 0.2)) # 调整重复提交的延迟范围
            elif result.get('errorCode') == 9997 and result.get('errorMsg') == "用户未登录":
                print("错误: 用户未登录。请更新您的Cookie！")
                return False # 停止重试，因为Cookie无效
            else:
                print(f"未知错误: {result.get('errorMsg')}")
            
            # 请求间隔
            if attempt < retry - 0.2:
                delay_time = delay + random.uniform(0.01, 0.2) # 调整每次重试的延迟范围
                print(f"等待 {delay_time:.2f} 秒后重试...")
                time.sleep(delay_time)
                
        except Exception as e:
            print(f"请求异常: {str(e)}")
            time.sleep(delay)
    
    return False

async def exchange_item_async(session, cookie, request_id):
    """异步兑换商品

    Args:
        session: aiohttp会话
        cookie: 用户Cookie
        request_id: 请求ID
    """
    url = "https://api.m.jd.com/api?functionId=SEP_EXCHANGE_SUBMIT"

    # 生成随机UUID和时间戳
    random_uuid = generate_uuid()
    timestamp = get_current_time()

    payload = {
        'appid': "h5-sep",
        'functionId': "SEP_EXCHANGE_SUBMIT",
        'body': json.dumps({
            "businessType": 118502,
            "activityId": 810602,
            "quantity": 1,
            "score": 2,
            "remark": "",
            "uuid": random_uuid,
            "addrCode": 5404598669,
            "exchangeWareType": 2,
            "wareBusinessId": "1192685891",
            "deliveryMode": 3,
            "activityWareId": 1180704,
            "parentActivityWareId": None,
            "parentActivityId": None,
            "stockType": 1,
            "upperBusinessType": None,
            "orderType": 2,
            "userName": "薛薛薛",
            "phoneNum": "***********",
            "userAddress": "广东清远市清城区东城街道象牙岭家和微超",
            "provinceId": 19,
            "cityId": 1704,
            "countyId": 37734,
            "townId": 56600,
            "addressDetail": "象牙岭家和微超",
            "_t": timestamp
        }),
        'client': "m",
        'clientVersion': "6.0.0"
    }

    headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'x-referer-page': "https://m-sep.jd.com/Settlement",
        'x-api-eid-token': "jdd03UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DMAAAAMXPUU2VVAAAAAAC2EI7XUUUTWUOAX",
        'x-rp-client': "h5_1.0.0",
        'origin': "https://m-sep.jd.com",
        'referer': "https://m-sep.jd.com/",
        'Cookie': cookie
    }

    try:
        async with session.post(url, data=payload, headers=headers) as response:
            result = await response.json()
            print(f"请求{request_id}: {json.dumps(result, ensure_ascii=False)}")

            if result.get('success'):
                print(f"🎉 请求{request_id} 兑换成功!")
                return True
            elif result.get('errorCode') == 3011:
                print(f"❌ 请求{request_id} 库存不足")
            elif result.get('errorCode') == 3001:
                print(f"⚠️ 请求{request_id} 重复提交")
            elif result.get('errorCode') == 9997 and result.get('errorMsg') == "用户未登录":
                print(f"🔐 请求{request_id} 用户未登录，Cookie无效")
            else:
                print(f"❓ 请求{request_id} 未知错误: {result.get('errorMsg')}")

            return False

    except Exception as e:
        print(f"💥 请求{request_id} 异常: {str(e)}")
        return False

async def batch_exchange_async(cookie, concurrent_count=50):
    """批量异步兑换

    Args:
        cookie: 用户Cookie
        concurrent_count: 并发请求数量，默认50个
    """
    # 创建SSL上下文，禁用证书验证
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE

    async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context)) as session:
        print(f"🚀 启动 {concurrent_count} 个并发请求...")

        # 创建并发任务
        tasks = [
            exchange_item_async(session, cookie, i+1)
            for i in range(concurrent_count)
        ]

        # 执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 统计结果
        success_count = sum(1 for result in results if result is True)
        print(f"📊 批量兑换完成: {success_count}/{concurrent_count} 成功")

        return success_count > 0

async def wait_until_target_time_jd(target_hour=10, target_minute=0, advance_ms=200):
    """等待到指定时间（基于京东服务器时间）"""
    # 获取京东服务器时间
    current_jd_time, time_diff = await get_jd_time()
    now = datetime.datetime.fromtimestamp(current_jd_time)
    target_time = now.replace(hour=target_hour, minute=target_minute, second=0, microsecond=0)

    # 如果目标时间已过，设置为明天的目标时间
    if now >= target_time:
        target_time += datetime.timedelta(days=1)

    target_timestamp = target_time.timestamp()

    print(f"京东服务器当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"目标兑换时间: {target_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"提前抢购: {advance_ms}毫秒")

    # 使用京东时间同步等待
    await wait_until_jd_time(target_timestamp, advance_ms)

async def scheduled_exchange():
    """定时兑换任务（基于京东服务器时间）"""
    # 内置Cookie
    cookie = "__jda=122270672.1750150929483324107970.1750150929.1750150929.1750150929.1; __jdv=122270672%7Cdirect%7C-%7Cnone%7C-%7C1750150929484; __jdc=122270672; mba_muid=1750150929483324107970; 3AB9D23F7A4B3C9B=UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DM; TrackerID=ALWJ94h4GXYOmMLyPJbNwQ-sWKOSECHJer1KXme55arH0lAMhDWPv77_Z9pwIo2IirZ9SS9StdAJgYmKBZQEnB__9r6W81AhLX2Cr8C8FdsMDoh3tU9vjFGMjvushxoC6XsUQEBYyJjm5GMfx736dw; pt_key=AAJoUS8fADAPTsz35xG0cXOXDl2K8hDdk_EimM1m_m9IFcA-rDrfkmoC_2U52nlgNCsSP5g5EYs; pt_pin=jd_IOPnkWamWXQT; pt_token=my73pjp0; pwdt_id=jd_IOPnkWamWXQT; sfstoken=tk01med791d27a8sMXgyKzN4Mjdyl7UpeBfwdzJvUhVEfuyVg/HET+mZFd/uSjpob7TvY+eOo5mh9fQ0WZW4KdQHhcBi; pt_st=1_rCOGd9aDH7Mvhf4ZRhMzJbDJygk8_TiVv_PtKYWfQ-RanGXYGLD9fRlzvUVEI4pEeVnnCSuM89r8sISmDkm2FpYwAoTqm4fbyqgVVWldLFpEKuvCtEMoWIDB4GmhuoPzEwjL2VUuWT28AYG51sw4lCJMa5xt_nkLE64WRo83n5IhNh4scTDc1dXVEpf0NfGg4EnVPuLctyfX02FKaRWoatphwV2wHoNJnCw1DRf0dCQL; __jdb=122270672.6.1750150929483324107970|1.1750150929; 3AB9D23F7A4B3CSS=jdd03UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DMAAAAMXPU6MDCQAAAAACW7KAAYQ7NWHYIX; _gia_d=1; sepCJSToken=jdd03UUCKIGC67D323MLW32227OCULPRCFBO5NAPQCTULNVNWKFTK5Z4I65J2AZXIPQGFQ5XOJCGJOKNDWJOHFB7Q7YJ4DMAAAAMXPU6MDCQAAAAACW7KAAYQ7NWHYIX; mba_sid=17501509294851566716052.14; sdtoken=AAbEsBpEIOVjqTAKCQtvQu17yH_EqEN7rcjtUJnNVPDFJHaNZZ1lHjTUtLj4WvQcnX8XpM5UQU0IM00ub61xnIHym5x_2B6L-8p1ym332oV2h8sVHBwREoIrpdCtzDlE82o; __jd_ref_cls=JDSEP_SubmitExchange"

    while True:
        # 等待到早上10点
        await wait_until_target_time_jd(10, 0)  # 等待到早上10点，提前200毫秒抢购

        print("=" * 50)
        current_jd_time, _ = await get_jd_time()
        print(f"开始执行定时兑换任务 - {datetime.datetime.fromtimestamp(current_jd_time).strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)

        # 执行批量异步兑换（50个并发请求）
        success = await batch_exchange_async(cookie, concurrent_count=50)

        if success:
            print("兑换成功！等待明天继续...")
        else:
            print("兑换失败，等待明天重试...")

        print("=" * 50)
        print("任务完成，等待下次执行...")
        #print("=" * 50)

# 使用示例
if __name__ == "__main__":
    print("京东商品定时兑换脚本启动（基于京东服务器时间）")
    print("每天早上10:00自动执行兑换任务")
    #print("支持提前200毫秒抢购")
    print("并发50个异步请求提高成功率")
    #print("按 Ctrl+C 停止脚本")
    #print("-" * 50)

    try:
        # 启动定时任务
        asyncio.run(scheduled_exchange())
    except KeyboardInterrupt:
        print("\n脚本已停止")
    except Exception as e:
        print(f"脚本运行异常: {str(e)}")
