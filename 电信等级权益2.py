# -*- coding: utf-8 -*-
import certifi
import requests
import re
import time
import json
import random
import datetime
import base64
import threading
import ssl
import execjs
import os
import sys
import binascii

from bs4 import BeautifulSoup

from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
from Crypto.Cipher import DES3
from Crypto.Util.Padding import pad, unpad
from Crypto.Util.strxor import strxor
from Crypto.Cipher import AES
from http import cookiejar  # Python 2: import cookielib as cookiejar
from requests.adapters import HTTPAdapter
# from requests.packages.urllib3.util.ssl_ import create_urllib3_context
# from tools.notify import send
from urllib3.util.ssl_ import create_urllib3_context

ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE

class BlockAll(cookiejar.CookiePolicy):
    return_ok = set_ok = domain_return_ok = path_return_ok = lambda self, *args, **kwargs: False
    netscape = True
    rfc2965 = hide_cookie2 = False


def printn(m):
    print(f'\n{m}')
ORIGIN_CIPHERS = ('DEFAULT@SECLEVEL=1')

ip_list = []
class DESAdapter(HTTPAdapter):
    def __init__(self, pool_connections=10, pool_maxsize=100, max_retries=3, *args, **kwargs):
        super().__init__(pool_connections, pool_maxsize, max_retries, *args, **kwargs)
        
    def init_poolmanager(self, pool_connections, pool_maxsize, block=False, *args, **kwargs):
        kwargs['ssl_context'] = ssl_context
        return super(DESAdapter, self).init_poolmanager(pool_connections, pool_maxsize, block, ssl_context=kwargs['ssl_context'])
        
    def proxy_manager_for(self, *args, **kwargs):
        kwargs['ssl_context'] = ssl_context
        return super(DESAdapter, self).proxy_manager_for(*args, **kwargs)

def aes_ecb_encrypt(plaintext, key):
    key = key.encode('utf-8')
    if len(key) not in [16, 24, 32]:
        raise ValueError("密钥长度必须为16/24/32字节")
    
    # 对明文进行PKCS7填充
    padded_data = pad(plaintext.encode('utf-8'), AES.block_size)
    #padded_data = plaintext.encode('utf-8')
    # 创建AES ECB加密器
    cipher = AES.new(key, AES.MODE_ECB)
    
    # 加密并返回Base64编码结果
    ciphertext = cipher.encrypt(padded_data)
    return base64.b64encode(ciphertext).decode('utf-8')

# 创建requests会话并应用ssl_context
ss = requests.session()
ss.ssl = ssl_context
ss.mount('https://', DESAdapter())

requests.packages.urllib3.disable_warnings()
ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE
ssl_context.set_ciphers('DEFAULT@SECLEVEL=0')
ss = requests.session()
ss.ssl = ssl_context
ss.headers = {
    "User-Agent": "Mozilla/5.0 (Linux; Android 13; 22081212C Build/TKQ1.220829.002) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.5112.97 Mobile Safari/537.36",
    "Referer": "https://wapact.189.cn:9001/JinDouMall/JinDouMall_independentDetails.html"}
ss.mount('https://', DESAdapter())
yc = 0.1
wt = 0
kswt = -3
yf = datetime.datetime.now().strftime("%Y%m")

# 全局事件，用于同步所有线程的倒计时
countdown_event = threading.Event()
countdown_lock = threading.Lock()
countdown_completed = False

data = {}
dirsize = 0
try:
    with open('权益id.log') as fr:
        data1 = fr.read()
        dirsize = len(data1)
        data = eval(data1)
except:
    with open('权益id.log', 'w') as f:
        pass

yf = datetime.datetime.now().strftime("%Y%m")
dd = datetime.datetime.now().strftime("%d")
#dd = '01'


def getId(s,phone,ck,accId=None):
    """获取权益ID并保存所有等级的权益ID"""
    if yf not in data:
        data[yf] = {}

    str1 = get_level(s,phone,ck,accId)
    if not str1:
        return

    # 保存所有等级的权益ID
    str2 = str1.split('#')
    for i in range(0, 3):
        if i < len(str2) and str2[i]:
            data[yf][f'{i + 4}'] = str2[i]



def get_level(s,phone,ck,accId=None):
    """修复版本：获取等级权益列表（参考等级权益兑换.py）

    Args:
        s: session对象
        phone: 手机号
        ck: cookies
        accId: 账户ID（新增必需参数）

    Returns:
        权益ID字符串，格式：id1#id2#id3#
    """
    try:
        # 使用新的参数结构
        if not accId:
            accId = phone

        value = {
            "type": "hg_qd_djqydh",
            "accId": accId,
            "shopId": "20001"
        }
        paraV = encrypt_paraNew(value)

        # 使用内置瑞数cookies
        if rs:
            bd = js.call('main').split('=')
            ck[bd[0]] = bd[1]

        response = s.post(
            'https://wappark.189.cn/jt-sign/paradise/queryLevelRightInfo',
            json={"para": paraV},
            cookies=ck,
            verify=certifi.where()
        )

        data = response.json()
        if data.get('code') == 401:
            return ''

        # 获取所有等级的话费权益ID
        rightsId = ''
        levelStr = ['V4','V5','V6']

        for level_key in levelStr:
            if level_key in data:
                right_list = data[level_key]
                for item in right_list:
                    title = item.get('title', '')
                    activity_id = item.get('activityId', '')
                    if '话费' in title and activity_id:
                        rightsId += f'{activity_id}#'

        return rightsId

    except Exception as e:
        try:
            # 重试时使用内置瑞数
            if rs:
                bd = js.call('main').split('=')
                ck[bd[0]] = bd[1]

            paraV = encrypt_para(json.dumps(value))
            response = s.post(
                'https://wappark.189.cn/jt-sign/paradise/getLevelRightsList',
                json={"para": paraV},
                cookies=ck,
                verify=certifi.where()
            )

            data = response.json()
            if data.get('code') == 401:
                return ''

            current_level = int(data['currentLevel'])
            key_name = 'V' + str(current_level)
            rightsId = ''

            for item in data.get(key_name, []):
                if '话费' in item.get('name', ''):
                    rightsId += f"{item['id']}#"

            return rightsId

        except Exception as e:
            return ''


wxp = {}
errcode = {
    "0": "兑换成功",
    "412": "兑换次数已达上限",
    "413": "商品已兑完",
    "420": "未知错误",
    "410": "该活动已失效~",
    "Y0001": "当前等级不足，去升级兑当前话费",
    "Y0002": "使用翼相连网络600分钟或连接并拓展网络500分钟可兑换此奖品",
    "Y0003": "使用翼相连共享流量400M或共享WIFI：2GB可兑换此奖品",
    "Y0004": "使用翼相连共享流量2GB可兑换此奖品",
    "Y0005": "当前等级不足，去升级兑当前话费",
    "E0001": "您的网龄不足10年，暂不能兑换"
}

# 加密参数
key = b'1234567`90koiuyhgtfrdews'
iv = 8 * b'\0'

public_key_b64 = '''-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBkLT15ThVgz6/NOl6s8GNPofdWzWbCkWnkaAm7O2LjkM1H7dMvzkiqdxU02jamGRHLX/ZNMCXHnPcW/sDhiFCBN18qFvy8g6VYb9QtroI09e176s+ZCtiv7hbin2cCTj99iUpnEloZm19lwHyo69u5UMiPMpq0/XKBO8lYhN/gwIDAQAB
-----END PUBLIC KEY-----'''

public_key_data = '''-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC+ugG5A8cZ3FqUKDwM57GM4io6JGcStivT8UdGt67PEOihLZTw3P7371+N47PrmsCpnTRzbTgcupKtUv8ImZalYk65dU8rjC/ridwhw9ffW2LBwvkEnDkkKKRi2liWIItDftJVBiWOh17o6gfbPoNrWORcAdcbpk2L+udld5kZNwIDAQAB
-----END PUBLIC KEY-----'''


def t(h, m=59, s=55, ms=0):
    """获取指定时间的时间戳，支持毫秒级精度
    
    Args:
        h: 小时
        m: 分钟，默认59
        s: 秒，默认55
        ms: 毫秒，默认0
    
    Returns:
        指定时间的时间戳（秒）
    """
    date = datetime.datetime.now()
    # 如果当前时间已经超过指定的小时，则获取下一天的时间
    if date.hour > h or (date.hour == h and date.minute > m) or (date.hour == h and date.minute == m and date.second > s):
        date = date + datetime.timedelta(days=1)
    
    date_zero = date.replace(year=date.year, month=date.month, day=date.day, 
                            hour=h, minute=m, second=s, microsecond=ms*1000)
    date_zero_time = time.mktime(date_zero.timetuple()) + (ms / 1000)
    return date_zero_time


def get_time_diff(target_time):
    """获取当前时间与目标时间的差值，精确到毫秒"""
    now_timestamp = time.time()
    time_diff = target_time - now_timestamp
    return time_diff


def countdown_wait(target_time, advance_ms=0):
    """倒计时等待功能，支持毫秒级精准控制，可提前指定毫秒数执行
    
    Args:
        target_time: 目标时间戳
        advance_ms: 提前执行的毫秒数，默认300毫秒
    
    Returns:
        执行时的时间戳字符串
    """
    time_diff = get_time_diff(target_time)
    
    # 减去提前量（毫秒转秒）
    time_diff = time_diff - (advance_ms / 1000)
    
    if time_diff <= 0:
        current_timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        print(f"当前时间已超过目标时间，立即执行，时间戳：{current_timestamp}")
        return current_timestamp
    
    target_datetime = datetime.datetime.fromtimestamp(target_time)
    print(f"倒计时等待开始，目标时间：{target_datetime.strftime('%Y-%m-%d %H:%M:%S.%f')}")
    print(f"将提前 {advance_ms} 毫秒执行，等待时间：{time_diff:.3f} 秒")
    
    # 计算开始时间用于记录时间戳
    start_wait = time.time()
    
    # 主要等待时间（留出50ms用于精确控制）
    if time_diff > 0.05:
        main_wait = time_diff - 0.05
        print(f"主要等待阶段：{main_wait:.3f}秒")
        time.sleep(main_wait)
    
    # 精确控制最后50ms
    print("进入精确控制阶段...")
    loop_start = time.time()
    while True:
        now = time.time()
        remaining = (target_time - now) - (advance_ms / 1000)
        if remaining <= 0:
            break
        # 使用极短的睡眠提高精度，Windows系统下可能精度有限
        time.sleep(0.001)
    
    # 记录实际等待时间和时间戳
    actual_wait = time.time() - start_wait
    precision_wait = time.time() - loop_start
    current_timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    
    print(f"倒计时结束，当前时间戳：{current_timestamp}")
    print(f"实际等待时间：{actual_wait:.3f}秒，精确控制阶段：{precision_wait:.3f}秒")
    print(f"目标时间与实际执行时间差：{(time.time() - (target_time - advance_ms/1000))*1000:.2f}毫秒")
    
    return current_timestamp


def encrypt(text):
    cipher = DES3.new(key, DES3.MODE_CBC, iv)
    ciphertext = cipher.encrypt(pad(text.encode(), DES3.block_size))
    return ciphertext.hex()


def decrypt(text):
    ciphertext = bytes.fromhex(text)
    cipher = DES3.new(key, DES3.MODE_CBC, iv)
    plaintext = unpad(cipher.decrypt(ciphertext), DES3.block_size)
    return plaintext.decode()


def b64(plaintext):
    public_key = RSA.import_key(public_key_b64)
    cipher = PKCS1_v1_5.new(public_key)
    ciphertext = cipher.encrypt(plaintext.encode())
    return base64.b64encode(ciphertext).decode()


def encrypt_para(plaintext):
    public_key = RSA.import_key(public_key_data)
    cipher = PKCS1_v1_5.new(public_key)
    ciphertext = cipher.encrypt(plaintext.encode())
    return ciphertext.hex()


def encrypt_paraNew(p):
    """新的加密函数，支持长参数加密"""
    k = RSA.import_key(public_key_data)
    c = PKCS1_v1_5.new(k)
    s = k.size_in_bytes() - 11
    d = p.encode() if isinstance(p, str) else json.dumps(p).encode()
    return binascii.hexlify(b''.join(c.encrypt(d[i:i+s]) for i in range(0, len(d), s))).decode()


def encode_phone(text):
    encoded_chars = []
    for char in text:
        encoded_chars.append(chr(ord(char) + 2))
    return ''.join(encoded_chars)


def ophone(t):
    key = b'34d7cb0bcdf07523'
    utf8_key = key.decode('utf-8')
    utf8_t = t.encode('utf-8')
    cipher = AES.new(key, AES.MODE_ECB)
    ciphertext = cipher.encrypt(pad(utf8_t, AES.block_size))
    return ciphertext.hex()


# def send(uid,content):
#     r = requests.post('https://wxpusher.zjiecode.com/api/send/message',json={"appToken":"AT_3hr0wdZn5QzPNBbpTHFXawoDIsSUmPkN","content":content,"contentType":1,"uids":[uid]}).json()
#     return r


def userLoginNormal(phone, password):
    alphabet = 'abcdef0123456789'
    uuid = [''.join(random.sample(alphabet, 8)), ''.join(random.sample(alphabet, 4)),
            '4' + ''.join(random.sample(alphabet, 3)), ''.join(random.sample(alphabet, 4)),
            ''.join(random.sample(alphabet, 12))]
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    loginAuthCipherAsymmertric = 'iPhone 14 15.4.' + uuid[0] + uuid[1] + phone + timestamp + password[:6] + '0$$$0.'

    r = ss.post('https://appgologin.189.cn:9031/login/client/userLoginNormal', json={
        "headerInfos": {"code": "userLoginNormal", "timestamp": timestamp, "broadAccount": "", "broadToken": "",
                        "clientType": "#11.3.0#channel35#Xiaomi Redmi K30 Pro#", "shopId": "20002", "source": "110003",
                        "sourcePassword": "Sid98s", "token": "", "userLoginName": encode_phone(phone)}, "content": {"attach": "test",
                                                                                                      "fieldData": {
                                                                                                          "loginType": "4",
                                                                                                          "accountType": "",
                                                                                                          "loginAuthCipherAsymmertric": b64(
                                                                                                              loginAuthCipherAsymmertric),
                                                                                                          "deviceUid":
                                                                                                              uuid[0] +
                                                                                                              uuid[1] +
                                                                                                              uuid[2],
                                                                                                          "phoneNum": encode_phone(
                                                                                                              phone),
                                                                                                          "isChinatelecom": "0",
                                                                                                          "systemVersion": "15.4.0",
                                                                                                          "authentication": encode_phone(password)}}}).json()

    l = r['responseData']['data']['loginSuccessResult']

    if l:
        load_token[phone] = l
        with open(load_token_file, 'w') as f:
            json.dump(load_token, f)
        ticket = get_ticket(phone, l['userId'], l['token'])
        return ticket

    return False


def get_ticket(phone, userId, token):
    r = ss.post('https://appgologin.189.cn:9031/map/clientXML',
                data='<Request><HeaderInfos><Code>getSingle</Code><Timestamp>' + datetime.datetime.now().strftime(
                    "%Y%m%d%H%M%S") + '</Timestamp><BroadAccount></BroadAccount><BroadToken></BroadToken><ClientType>#9.6.1#channel50#iPhone 14 Pro Max#</ClientType><ShopId>20002</ShopId><Source>110003</Source><SourcePassword>Sid98s</SourcePassword><Token>' + token + '</Token><UserLoginName>' + phone + '</UserLoginName></HeaderInfos><Content><Attach>test</Attach><FieldData><TargetId>' + encrypt(
                    userId) + '</TargetId><Url>4a6862274835b451</Url></FieldData></Content></Request>',
                headers={'user-agent': 'CtClient;10.4.1;Android;13;22081212C;NTQzNzgx!#!MTgwNTg1'})

    # printn(phone, '获取ticket', re.findall('<Reason>(.*?)</Reason>',r.text)[0])

    tk = re.findall('<Ticket>(.*?)</Ticket>', r.text)
    if len(tk) == 0:
        return False

    return decrypt(tk[0])


def queryInfo(phone, s):
    global rs
    a = 1
    while a < 10:
        if rs:
            bd = js.call('main').split('=')
            ck[bd[0]] = bd[1]

        r = s.get('https://wapact.189.cn:9001/gateway/golden/api/queryInfo', cookies=ck).json()

        try:
            printn(f'{phone} 金豆余额 {r["biz"]["amountTotal"]}')
            amountTotal = r["biz"]["amountTotal"]
        except:
            amountTotal = 0
        if amountTotal < 3000:
            if rs == 1:
                bd = js.call('main').split('=')
                ck[bd[0]] = bd[1]

            res = s.post('http://wapact.189.cn:9000/gateway/stand/detail/exchange', json={"activityId": jdaid},
                         cookies=ck).text

            if '$_ts=window' in res:
                first_request()
                rs = 1

            time.sleep(3)
        else:
            return r
        a += 1

    return r


def getSign(ticket,session):
    """修复版本：获取sign（使用内置瑞数）"""
    try:
        # 使用内置瑞数cookies
        if rs:
            bd = js.call('main').split('=')
            ck[bd[0]] = bd[1]

        # 使用内置瑞数发送请求
        response = session.get(
            'https://wappark.189.cn/jt-sign/ssoHomLogin?ticket=' + ticket,
            cookies=ck
        ).json()

        if response.get('resoultCode') == '0':
            sign = response.get('sign')
            accId = response.get('accId')
            #print(f"获取sign成功: {sign}")
            #print(f"获取accId成功: {accId}")
            return sign, accId
        else:
            print(f"获取sign失败[{response.get('resoultCode')}]: {response}")
    except Exception as e:
        print(f"getSign 发生错误: {str(e)}")
    return None

def level_ex(phone, rightsId, session, ck, accId=None):
    """修复版本：兑换权益（修复accId问题）"""
    try:
        # 刷新瑞数参数
        bd = js.call('main').split('=')
        ck[bd[0]] = bd[1]
        now = datetime.datetime.now().strftime('%H:%M:%S.%f')

        # 修复：使用正确的accId
        if not accId:
            # 如果没有传入accId，尝试从getSign获取
            print("⚠️  警告：没有传入accId，使用phone作为默认值")
            accId = phone

        # 修复：使用新的参数结构
        value = {
            "id": rightsId,  # 使用activityId作为id
            "accId": accId,  # 使用正确的accId
            "showType": "9003",  # 固定值
            "showEffect": "8",   # 固定值
            "czValue": "0"       # 固定值
        }

        # 使用encrypt_paraNew加密
        data = {"para": encrypt_paraNew(value)}

        #print(f'{now}--兑换参数: {value}')

        response = session.post('https://wappark.189.cn/jt-sign/paradise/receiverRights', json=data,cookies=ck)
        result = response.text
        print(f'{now}--Phone:{phone}--{result}')

        # 检查是否返回了瑞数验证页面
        if '<!DOCTYPE html' in result and '$_ts=window' in result:
            print(f"⚠️  {phone} 触发瑞数验证，重新获取参数后重试")
            # 重新刷新瑞数参数
            bd = js.call('main').split('=')
            ck[bd[0]] = bd[1]
            time.sleep(0.2)  # 短暂延迟
            return False  # 返回False以触发重试

        # 检查响应结果
        if '"resoultCode":"0"' in result or '权益已兑换' in result:
            return True  # 兑换成功，返回True
        elif '请勿重复点击' in result:
            time.sleep(0.5)  
            return False  # 继续重试
        else:
            time.sleep(0.5)  # 其他情况添加0.5秒延迟
            return False  # 继续重试
            
    except Exception as e:
        print(e)
        time.sleep(1)  # 发生异常时添加1秒延迟
        return False

def ks(phone, ticket,level, uid):
    global wt

    wxp[phone] = uid
    s = requests.session()
    s.headers = {
        "User-Agent": "Mozilla/5.0 (Linux; Android 13; 22081212C Build/TKQ1.220829.002) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.5112.97 Mobile Safari/537.36",
        "Referer": "https://wapact.189.cn:9001/JinDouMall/JinDouMall_independentDetails.html"}
    s.cookies.set_policy(BlockAll())
    s.mount('https://', DESAdapter())
    s.timeout = 10
    if rs:
        bd = js.call('main').split('=')
        ck[bd[0]] = bd[1]

    dataParam = aes_ecb_encrypt(json.dumps({"ticket":ticket,"backUrl":"https%3A%2F%2Fwapact.189.cn%3A9001","platformCode":"P201010301","loginType":2}), 'telecom_wap_2018')
    login = s.post('https://wapact.189.cn:9001/unified/user/login',data=dataParam, headers={"Content-Type":"application/json;charset=UTF-8","Accept":"application/json, text/javascript, */*; q=0.01"}, cookies=ck).json()
    if login['code'] == 0:
        printn(phone + " 获取token成功")
        s.headers["Authorization"] = "Bearer " + login["biz"]["token"]

        queryInfo(phone, s)

        if rs:
            bd = js.call('main').split('=')
            ck[bd[0]] = bd[1]
            response_data = s.get('https://wappark.189.cn/jt-sign/ssoHomLogin?ticket=' + ticket, cookies=ck).json()['sign']
            new_header = {
                "User-Agent": f"CtClient;9.6.1;Android;12;SM-G9860;{base64.b64encode(phone[5:11].encode()).decode().strip('=+')}!#!{base64.b64encode(phone[0:5].encode()).decode().strip('=+')}",
                "Referer": "https://wapside.189.cn:9001/resources/dist/signInActivity.html",
                "sign":response_data}
            s.headers.update(new_header)

            # 获取sign用于后续接口调用（修复：处理返回值）
            sign_result = getSign(ticket, s)
            if sign_result:
                sign, accId = sign_result
                #print(f"获取到sign: {sign}, accId: {accId}")
            else:
                print("获取sign失败")
                return

            # 检查是否需要重新获取权益ID
            need_refresh = (dd == '01' or dirsize == 0 or
                           yf not in data or
                           level not in data.get(yf, {}) or
                           not data.get(yf, {}).get(level) or
                           data.get(yf, {}).get(level) == "")

            if need_refresh:
                s.headers = {
                    "User-Agent": "Mozilla/5.0 (Linux; Android 13; 22081212C Build/TKQ1.220829.002) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.5112.97 Mobile Safari/537.36",
                    "sign": sign
                }

                getId(s,phone,ck,accId)

                if yf not in data or not data[yf]:
                    return

                with open('权益id.log', 'w') as f:
                    f.write(json.dumps(data))

            # 检查权益ID是否存在
            if yf not in data or level not in data[yf]:
                return

            rightsId = data[yf][level]
            #print(f"✅ 获取到权益ID：{rightsId}")
            new_header = {
                "User-Agent": f"CtClient;9.6.1;Android;12;SM-G9860;{base64.b64encode(phone[5:11].encode()).decode().strip('=+')}!#!{base64.b64encode(phone[0:5].encode()).decode().strip('=+')}",
                "Referer": "https://wapside.189.cn:9001/resources/dist/signInActivity.html",
                "sign": sign}
            s.headers.update(new_header)
            # 同步倒计时逻辑
            global countdown_completed
            with countdown_lock:  # 使用锁确保只有一个线程执行倒计时
                if not countdown_completed:
                    # 第一个到达的线程负责倒计时
                    current_hour = datetime.datetime.now().hour
                    current_minute = datetime.datetime.now().minute
                    target_time = None

                    if current_hour == 13 and current_minute >= 5:
                        target_time = t(13, 13, 0, 0)
                        print(f"即将到4点11分，设置目标时间为4点11分整")
                    elif current_hour == 0 and current_minute == 0:
                        target_time = t(0, 0, 0, 0)
                        print(f"当前已是0点，设置目标时间为0点整")

                    if target_time and target_time > time.time():
                        print(f"{phone} 负责倒计时，其他线程等待...")
                        countdown_completed = True  # 先设置标志，防止其他线程进入
                        countdown_event.clear()  # 清除事件，让其他线程等待
                        # 释放锁后再执行倒计时，避免阻塞其他线程
                    else:
                        print(f"{phone} 立即开始，通知所有线程")
                        countdown_completed = True
                        countdown_event.set()  # 立即通知所有线程
                        target_time = None  # 标记不需要倒计时
                else:
                    target_time = None  # 其他线程不需要倒计时

            # 在锁外执行倒计时（如果需要）
            if target_time and target_time > time.time():
                timestamp = countdown_wait(target_time, advance_ms=500)
                print(f"倒计时完成，通知所有线程开始兑换，时间戳: {timestamp}")
                countdown_event.set()  # 通知所有等待的线程
            elif not countdown_event.is_set():
                # 其他线程等待倒计时完成
                print(f"{phone} 等待倒计时完成...")
                countdown_event.wait()  # 等待事件被设置
                print(f"{phone} 收到开始信号，开始兑换")

            print(f"{phone} 开始兑换")

            # 添加随机延迟，错开请求时间，避免同时触发瑞数防护
            import random
            delay = random.uniform(0.05, 0.3)  # 50-200ms随机延迟
            time.sleep(delay)

            # 兑换循环 - 最多重试3次
            max_retries = 3
            for retry_count in range(max_retries):
                try:
                    result = level_ex(phone, rightsId, s, ck, accId)

                    if result == True:
                        print(f"{phone} 兑换成功")
                        break
                    elif result == "already":
                        print(f"{phone} 已经领取过该权益或奖品已领完")
                        break
                    else:
                        # 兑换失败，继续重试
                        if retry_count < max_retries - 1:
                            time.sleep(0.2)  # 重试间隔
                except Exception as e:
                    if retry_count == max_retries - 1:
                        print(f"{phone} 兑换失败")
                        break
                    time.sleep(0.2)
            else:
                print(f"{phone} 兑换失败")

    else:
        printn(f"{phone} 获取token {login['message']}")

def first_request(res=''):
    global js, fw
    url = 'https://wapact.189.cn:9001/gateway/standExchange/detailNew/exchange'
    if res == '':
        response = ss.get(url)
        res = response.text
    soup = BeautifulSoup(res, 'html.parser')
    scripts = soup.find_all('script')
    for script in scripts:
        if 'src' in str(script):
            rsurl = re.findall('src="([^"]+)"', str(script))[0]

        if '$_ts=window' in script.get_text():
            ts_code = script.get_text()

    urls = url.split('/')
    rsurl = urls[0] + '//' + urls[2] + rsurl
    # print(rsurl)
    ts_code += ss.get(rsurl).text
    content_code = soup.find_all('meta')[1].get('content')
    # with open("瑞数通杀.js", encoding='utf-8') as f:
    #     js_code_ym = f.read()
    js_code_file = '''

    delete __filename
    delete __dirname
    ActiveXObject = undefined

    window = global;


    content="content_code"


    navigator = {"platform": "Linux aarch64"}
    navigator = {"userAgent": "CtClient;11.0.0;Android;13;22081212C;NTIyMTcw!#!MTUzNzY"}

    location={
        "href": "https://",
        "origin": "",
        "protocol": "",
        "host": "",
        "hostname": "",
        "port": "",
        "pathname": "",
        "search": "",
        "hash": ""
    }

    i = {length: 0}
    base = {length: 0}
    div = {
        getElementsByTagName: function (res) {
            console.log('div中的getElementsByTagName：', res)
            if (res === 'i') {
                return i
            }
        return '<div></div>'

        }
    }

    script = {

    }
    meta = [
        {charset:"UTF-8"},
        {
            content: content,
            getAttribute: function (res) {
                console.log('meta中的getAttribute：', res)
                if (res === 'r') {
                    return 'm'
                }
            },
            parentNode: {
                removeChild: function (res) {
                    console.log('meta中的removeChild：', res)
                    
                return content
                }
            },
            
        }
    ]
    form = '<form></form>'


    window.addEventListener= function (res) {
            console.log('window中的addEventListener:', res)
            
        }
        

    document = {

    
        createElement: function (res) {
            console.log('document中的createElement：', res)
            
            
        if (res === 'div') {
                return div
            } else if (res === 'form') {
                return form
            }
            else{return res}
                
            


        },
        addEventListener: function (res) {
            console.log('document中的addEventListener：', res)
            
        },
        appendChild: function (res) {
            console.log('document中的appendChild：', res)
            return res
        },
        removeChild: function (res) {
            console.log('document中的removeChild：', res)
        },
        getElementsByTagName: function (res) {
            console.log('document中的getElementsByTagName：', res)
            if (res === 'script') {
                return script
            }
            if (res === 'meta') {
                return meta
            }
            if (res === 'base') {
                return base
            }
        },
        getElementById: function (res) {
            console.log('document中的getElementById：', res)
            if (res === 'root-hammerhead-shadow-ui') {
                return null
            }
        }

    }

    setInterval = function () {}
    setTimeout = function () {}
    window.top = window


    'ts_code'



    function main() {
        cookie = document.cookie.split(';')[0]
        return cookie
    }

'''

    js_code_ym=js_code_file

    js_code = js_code_ym.replace('content_code', content_code).replace("'ts_code'", ts_code)
    js = execjs.compile(js_code)

    for cookie in ss.cookies:
        ck[cookie.name] = cookie.value
    return content_code, ts_code, ck




def main():
    global wt, rs
    r = ss.get('https://wapact.189.cn:9001/gateway/standExchange/detailNew/exchange',verify=certifi.where())
    if '$_ts=window' in r.text:
        rs = 1
        print("瑞数加密已开启")
        first_request()
    else:
        print("瑞数加密已关闭")
        rs = 0
    chinaTelecomAccount = '***********@182138@6&***********@182138@6&***********@182138@5'
    #if os.environ.get('chinaTelecomAccount3') != None:
    #    chinaTelecomAccount = os.environ.get('chinaTelecomAccount3')
    #else:
    #    print('添加chinaTelecomAccount3环境变量啊')

    for i in chinaTelecomAccount.split('&'):

        i = i.split('@')
        phone = i[0]
        password = i[1]
        level = i[2]
        uid = i[1]
        ticket = False

        # ticket = get_userTicket(phone)

        if phone in load_token:
            printn(f'{phone} 使用缓存登录')
            ticket = get_ticket(phone, load_token[phone]['userId'], load_token[phone]['token'])

        if ticket == False:
            printn(f'{phone} 使用密码登录')
            ticket = userLoginNormal(phone, password)

        if ticket:
            threading.Thread(target=ks, args=(phone, ticket,level,uid)).start()

            time.sleep(1)
        else:
            printn(f'{phone} 登录失败')


jdhf = ""
cfcs = 5
jdaid = '60dd79533dc03d3c76bdde30'
ck = {}
load_token_file = 'chinaTelecom_cache.json'
try:
    with open(load_token_file, 'r') as f:
        load_token = json.load(f)
except:
    load_token = {}

main()
